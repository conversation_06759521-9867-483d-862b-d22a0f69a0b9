#!/usr/bin/env python
import os
import sys
import django

# Configurar Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'controle_estoque.settings')
django.setup()

from estoque.models import EstoqueSemRevenir
from estoque.views import gerar_pdf_estoque_sem_revenir

def testar_pdf():
    print("=== Testando Geração de PDF ===")
    
    # Buscar estoques sem revenir
    estoques = EstoqueSemRevenir.objects.filter(quantidade__gt=0)
    print(f"Estoques encontrados: {estoques.count()}")
    
    if estoques.count() > 0:
        try:
            # Testar geração de PDF
            response = gerar_pdf_estoque_sem_revenir(estoques)
            print(f"PDF gerado com sucesso! Tamanho: {len(response.content)} bytes")
            
            # Salvar PDF para teste
            with open('teste_relatorio_sem_revenir.pdf', 'wb') as f:
                f.write(response.content)
            print("PDF salvo como 'teste_relatorio_sem_revenir.pdf'")
            
        except Exception as e:
            print(f"Erro ao gerar PDF: {e}")
            import traceback
            traceback.print_exc()
    else:
        print("Nenhum estoque sem revenir encontrado")

if __name__ == '__main__':
    testar_pdf()
