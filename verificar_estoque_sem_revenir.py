#!/usr/bin/env python
import os
import sys
import django

# Configurar Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'controle_estoque.settings')
django.setup()

from estoque.models import EstoqueSemRevenir, <PERSON><PERSON>

def verificar_dados():
    print("=== Verificação do Estoque Sem Revenir ===")
    
    # Verificar quantas molas existem
    total_molas = Mola.objects.count()
    print(f"Total de molas cadastradas: {total_molas}")
    
    # Verificar quantos estoques sem revenir existem
    total_estoques = EstoqueSemRevenir.objects.count()
    print(f"Total de registros de estoque sem revenir: {total_estoques}")
    
    # Verificar estoques com quantidade > 0
    estoques_com_quantidade = EstoqueSemRevenir.objects.filter(quantidade__gt=0)
    print(f"Estoques sem revenir com quantidade > 0: {estoques_com_quantidade.count()}")
    
    # Listar alguns exemplos
    print("\n=== Primeiros 5 registros ===")
    for estoque in estoques_com_quantidade[:5]:
        print(f"Mola: {estoque.mola.codigo} | Cliente: {estoque.mola.cliente} | Quantidade: {estoque.quantidade}")
    
    # Se não há dados, criar alguns para teste
    if estoques_com_quantidade.count() == 0:
        print("\n=== Criando dados de teste ===")
        
        # Pegar algumas molas para criar estoque sem revenir
        molas_teste = Mola.objects.all()[:3]
        
        for i, mola in enumerate(molas_teste, 1):
            estoque, created = EstoqueSemRevenir.objects.get_or_create(
                mola=mola,
                defaults={
                    'quantidade': i * 10,
                    'observacoes': f'Teste {i}'
                }
            )
            if created:
                print(f"Criado estoque sem revenir para mola {mola.codigo} com quantidade {i * 10}")
            else:
                estoque.quantidade = i * 10
                estoque.save()
                print(f"Atualizado estoque sem revenir para mola {mola.codigo} com quantidade {i * 10}")

if __name__ == '__main__':
    verificar_dados()
