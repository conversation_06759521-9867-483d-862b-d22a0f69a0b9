{% extends 'estoque/base.html' %}
{% load static %}

{% block title %}Estoque Sem Revenir - Molas <PERSON>{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Estoque Sem Revenir</h1>
        <div>
            <a href="{% url 'adicionar-mola-estoque-sem-revenir' %}" class="btn btn-primary">
                <i class="fas fa-plus"></i> Adiciona<PERSON>
            </a>
            <a href="{% url 'relatorio-estoque-sem-revenir' %}" class="btn btn-info">
                <i class="fas fa-chart-bar"></i> Relatório
            </a>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold">Filtros</h6>
        </div>
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-3">
                    <label for="id_codigo" class="form-label">Código</label>
                    <input type="text" name="codigo" id="id_codigo" class="form-control" value="{{ filter.form.codigo.value|default:'' }}">
                </div>
                <div class="col-md-3">
                    <label for="id_cliente" class="form-label">Cliente</label>
                    <input type="text" name="cliente" id="id_cliente" class="form-control" value="{{ filter.form.cliente.value|default:'' }}">
                </div>
                <div class="col-md-3">
                    <label for="id_material_padrao" class="form-label">Material</label>
                    <select name="material_padrao" id="id_material_padrao" class="form-select">
                        <option value="">---------</option>
                        {% for material in filter.form.fields.material_padrao.queryset %}
                            <option value="{{ material.id }}" {% if filter.form.material_padrao.value|stringformat:'s' == material.id|stringformat:'s' %}selected{% endif %}>
                                {{ material }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-12">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> Filtrar
                    </button>
                    <a href="{% url 'estoque-sem-revenir-list' %}" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Limpar Filtros
                    </a>
                </div>
            </form>
        </div>
    </div>

    <div class="card shadow">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold">Lista de Estoque Sem Revenir</h6>
            <small class="text-muted">Última atualização: {% now "d/m/Y H:i:s" %}</small>
        </div>
        <div class="card-body">
            {% if estoques %}
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>Cód</th>
                                <th>Mola</th>
                                <th>Cliente</th>
                                <th>Material</th>
                                <th>Diâmetro</th>
                                <th>Estoque</th>
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for estoque in estoques %}
                                <tr>
                                    <td>{{ estoque.mola.codigo }}</td>
                                    <td>{{ estoque.mola.nome_mola|default:"--" }}</td>
                                    <td>{{ estoque.mola.cliente }}</td>
                                    <td>
                                        {% if estoque.mola.material_padrao %}
                                            {{ estoque.mola.material_padrao.nome }}
                                        {% elif estoque.mola.material %}
                                            {{ estoque.mola.material.nome }}
                                        {% else %}
                                            <span class="text-muted">Não informado</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if estoque.mola.diametro %}
                                            {{ estoque.mola.diametro }}
                                        {% elif estoque.mola.material_padrao %}
                                            {{ estoque.mola.material_padrao.diametro }}
                                        {% elif estoque.mola.material %}
                                            {{ estoque.mola.material.diametro }}
                                        {% else %}
                                            <span class="text-muted">--</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ estoque.quantidade }}</td>
                                    <td>
                                        <a href="{% url 'mola-detail' estoque.mola.id %}" class="btn btn-sm btn-info" data-bs-toggle="tooltip" data-bs-placement="top" title="Informações da Mola">
                                            <i class="fas fa-info-circle"></i>
                                        </a>
                                        <a href="{% url 'excluir-estoque-sem-revenir' estoque.id %}" class="btn btn-sm btn-danger" data-bs-toggle="tooltip" data-bs-placement="top" title="Remover do Estoque">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Paginação -->
                {% if is_paginated %}
                    <nav aria-label="Navegação de páginas">
                        <ul class="pagination justify-content-center">
                            {% if page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page=1{% if request.GET.codigo %}&codigo={{ request.GET.codigo }}{% endif %}{% if request.GET.cliente %}&cliente={{ request.GET.cliente }}{% endif %}{% if request.GET.material_padrao %}&material_padrao={{ request.GET.material_padrao }}{% endif %}">Primeira</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.codigo %}&codigo={{ request.GET.codigo }}{% endif %}{% if request.GET.cliente %}&cliente={{ request.GET.cliente }}{% endif %}{% if request.GET.material_padrao %}&material_padrao={{ request.GET.material_padrao }}{% endif %}">Anterior</a>
                                </li>
                            {% endif %}

                            <li class="page-item active">
                                <span class="page-link">
                                    Página {{ page_obj.number }} de {{ page_obj.paginator.num_pages }}
                                </span>
                            </li>

                            {% if page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.codigo %}&codigo={{ request.GET.codigo }}{% endif %}{% if request.GET.cliente %}&cliente={{ request.GET.cliente }}{% endif %}{% if request.GET.material_padrao %}&material_padrao={{ request.GET.material_padrao }}{% endif %}">Próxima</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.codigo %}&codigo={{ request.GET.codigo }}{% endif %}{% if request.GET.cliente %}&cliente={{ request.GET.cliente }}{% endif %}{% if request.GET.material_padrao %}&material_padrao={{ request.GET.material_padrao }}{% endif %}">Última</a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                {% endif %}
            {% else %}
                <div class="alert alert-info text-center">
                    <i class="fas fa-info-circle"></i>
                    Nenhuma mola encontrada no estoque sem revenir.
                    <br>
                    <a href="{% url 'adicionar-mola-estoque-sem-revenir' %}" class="btn btn-primary mt-2">
                        <i class="fas fa-plus"></i> Adicionar Primeira Mola
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
