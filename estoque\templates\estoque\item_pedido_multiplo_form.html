{% extends 'estoque/base.html' %}

{% block title %}Adicionar <PERSON> Itens ao Pedido - Molas <PERSON>s{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Adicionar <PERSON>los Itens ao Pedido #{{ pedido.numero_pedido }}</h1>
        <a href="{% url 'pedido-venda-detail' pedido.id %}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Voltar
        </a>
    </div>

    <div class="card shadow">
        <div class="card-body">
            <form method="post" id="form-itens-multiplos" class="row g-3">
                {% csrf_token %}

                <div class="col-12 mt-4">
                    <h4>Molas</h4>
                    <p class="text-muted">Selecione as molas e informe as quantidades para adicionar ao pedido</p>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i> <strong>Dica:</strong> Pressione a tecla <kbd>Enter</kbd> para navegar entre os campos e adicionar novas linhas automaticamente.
                    </div>
                </div>

                <div id="molas-container">
                    <div class="row mb-2 mola-row" id="mola-row-1">
                        <div class="col-md-5">
                            <input type="text" name="nome_mola_1" class="form-control nome-mola" placeholder="Nome da mola (ex: 01, 02, etc)">
                        </div>
                        <div class="col-md-3">
                            <input type="number" name="quantidade_1" class="form-control quantidade-mola" placeholder="Quantidade" min="1">
                        </div>
                        <div class="col-md-2">
                            <div class="form-check d-none">
                                <input type="checkbox" name="movimentar_estoque_1" id="movimentar_estoque_1" class="form-check-input movimentar-estoque" checked>
                                <label for="movimentar_estoque_1" class="form-check-label">Movimentar</label>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <button type="button" class="btn btn-danger btn-remover-mola" data-row="1">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="col-12">
                    <button type="button" class="btn btn-info" id="btn-adicionar-mola">
                        <i class="fas fa-plus"></i> Adicionar Mola
                    </button>
                </div>

                <div class="col-12 mt-4">
                    <div class="form-check mb-3">
                        <input type="checkbox" name="movimentar_estoque_geral" id="movimentar_estoque_geral" class="form-check-input" checked>
                        <label for="movimentar_estoque_geral" class="form-check-label">Movimentar estoque para todos os itens</label>
                    </div>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Salvar
                    </button>
                    <a href="{% url 'pedido-venda-detail' pedido.id %}" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Cancelar
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        let rowCount = 1;

        // Função para adicionar nova linha de mola
        function adicionarNovaLinha() {
            rowCount++;

            const container = document.getElementById('molas-container');
            const newRow = document.createElement('div');
            newRow.className = 'row mb-2 mola-row';
            newRow.id = `mola-row-${rowCount}`;

            newRow.innerHTML = `
                <div class="col-md-5">
                    <input type="text" name="nome_mola_${rowCount}" class="form-control nome-mola" placeholder="Nome da mola (ex: 01, 02, etc)">
                </div>
                <div class="col-md-3">
                    <input type="number" name="quantidade_${rowCount}" class="form-control quantidade-mola" placeholder="Quantidade" min="1">
                </div>
                <div class="col-md-2">
                    <div class="form-check d-none">
                        <input type="checkbox" name="movimentar_estoque_${rowCount}" id="movimentar_estoque_${rowCount}" class="form-check-input movimentar-estoque" checked>
                        <label for="movimentar_estoque_${rowCount}" class="form-check-label">Movimentar</label>
                    </div>
                </div>
                <div class="col-md-2">
                    <button type="button" class="btn btn-danger btn-remover-mola" data-row="${rowCount}">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            `;

            container.appendChild(newRow);

            // Adicionar evento ao novo botão de remover
            newRow.querySelector('.btn-remover-mola').addEventListener('click', function() {
                const rowId = this.getAttribute('data-row');
                document.getElementById(`mola-row-${rowId}`).remove();
            });

            // Adicionar eventos de tecla para os novos campos
            adicionarEventosTecla(newRow);

            // Focar no campo de nome da mola recém-adicionado
            newRow.querySelector('.nome-mola').focus();

            return newRow;
        }

        // Função para adicionar eventos de tecla
        function adicionarEventosTecla(row) {
            const nomeMolaInput = row.querySelector('.nome-mola');
            const quantidadeInput = row.querySelector('.quantidade-mola');

            // Ao pressionar Enter no campo de nome da mola, vai para o campo de quantidade
            nomeMolaInput.addEventListener('keydown', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    quantidadeInput.focus();
                }
            });

            // Ao pressionar Enter no campo de quantidade, adiciona nova linha
            quantidadeInput.addEventListener('keydown', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    adicionarNovaLinha();
                }
            });
        }

        // Adicionar eventos de tecla à primeira linha
        adicionarEventosTecla(document.querySelector('.mola-row'));

        // Adicionar nova linha de mola (botão)
        document.getElementById('btn-adicionar-mola').addEventListener('click', function() {
            adicionarNovaLinha();
        });

        // Remover linha de mola (para a primeira linha)
        document.querySelector('.btn-remover-mola').addEventListener('click', function() {
            const rowId = this.getAttribute('data-row');
            document.getElementById(`mola-row-${rowId}`).remove();
        });

        // Validação do formulário
        document.getElementById('form-itens-multiplos').addEventListener('submit', function(e) {
            const rows = document.querySelectorAll('.mola-row');
            let valid = false;

            // Verificar se pelo menos uma mola foi preenchida
            rows.forEach(row => {
                const nomeMola = row.querySelector('input[name^="nome_mola_"]').value;
                const quantidade = row.querySelector('input[name^="quantidade_"]').value;

                if (nomeMola && quantidade) {
                    valid = true;
                }
            });

            if (!valid) {
                e.preventDefault();
                alert('Adicione pelo menos uma mola com quantidade válida.');
            }
        });
    });
</script>
{% endblock %}
{% endblock %}
