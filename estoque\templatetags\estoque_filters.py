from django import template

register = template.Library()

@register.filter
def abreviar_material(nome_material):
    """
    Converte nomes completos de materiais para abreviações.
    
    Args:
        nome_material (str): Nome completo do material
    
    Returns:
        str: Nome abreviado do material
    """
    if not nome_material:
        return "-"
    
    # Dicionário de abreviações específicas
    abreviacoes = {
        "Aço Carbono - Classe B": "Classe B",
        "Aço Inox 302": "Inox 302", 
        "Arame Latonado": "Latonado",
    }
    
    # Verifica se existe uma abreviação específica
    if nome_material in abreviacoes:
        return abreviacoes[nome_material]
    
    # Se não encontrar abreviação específica, aplica regras gerais
    nome_abreviado = nome_material
    
    # Remove "Aço Carbono - " do início
    if nome_abreviado.startswith("Aço Carbono - "):
        nome_abreviado = nome_abreviado.replace("Aço Carbono - ", "")
    
    # Substitui "Aço Inox" por "Inox"
    if "Aço Inox" in nome_abreviado:
        nome_abreviado = nome_abreviado.replace("Aço Inox", "Inox")
    
    # Substitui "Arame" por abreviação quando apropriado
    if nome_abreviado.startswith("Arame "):
        nome_abreviado = nome_abreviado.replace("Arame ", "")
    
    return nome_abreviado

@register.filter
def div(value, arg):
    """
    Divide value por arg.
    """
    try:
        return int(value) // int(arg)
    except (ValueError, ZeroDivisionError):
        return 0
