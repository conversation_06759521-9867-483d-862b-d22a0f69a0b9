"""
Script para corrigir problemas de timezone nos dados existentes.

Este script deve ser executado com:
python manage.py shell < fix_timezone.py
"""

import os
import django

# Configurar o ambiente Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'controle_estoque.settings')
django.setup()

from django.utils import timezone
from estoque.models import MovimentacaoEstoque, MovimentacaoMaterial

def fix_timezone_issues():
    """
    Corrige problemas de timezone em todos os registros de movimentação.
    """
    print("Corrigindo problemas de timezone...")
    
    # Corrigir MovimentacaoEstoque
    movimentacoes_estoque = MovimentacaoEstoque.objects.all()
    count_estoque = 0
    
    for mov in movimentacoes_estoque:
        if timezone.is_naive(mov.data):
            mov.data = timezone.make_aware(mov.data)
            mov.save(update_fields=['data'])
            count_estoque += 1
    
    # Corrigir MovimentacaoMaterial
    movimentacoes_material = MovimentacaoMaterial.objects.all()
    count_material = 0
    
    for mov in movimentacoes_material:
        if timezone.is_naive(mov.data):
            mov.data = timezone.make_aware(mov.data)
            mov.save(update_fields=['data'])
            count_material += 1
    
    print(f"Corrigidos {count_estoque} registros de MovimentacaoEstoque")
    print(f"Corrigidos {count_material} registros de MovimentacaoMaterial")
    print("Concluído!")

if __name__ == "__main__":
    fix_timezone_issues()
