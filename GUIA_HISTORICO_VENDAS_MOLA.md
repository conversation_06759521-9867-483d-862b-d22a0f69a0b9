# Guia Completo: Visualizar Histórico de Vendas de uma Mola Específica

## 📋 Informações Exibidas no Sistema

O sistema oferece as seguintes informações sobre vendas de uma mola específica:

### 📊 Dados Disponíveis
- ✅ **Datas das vendas/movimentações** (formato: dd/mm/aaaa hh:mm)
- ✅ **Quantidades vendidas** em cada transação
- ✅ **Tipo de movimentação** (Movimentação de Estoque, Pedido)
- ✅ **Números dos pedidos** (quando disponível)
- ✅ **Observações** de cada venda
- ⚠️ **Cliente por venda**: Não disponível diretamente (apenas cliente da mola)

### 🔍 Tipos de Vendas Rastreadas
1. **Vendas com Movimentação de Estoque**: Saídas registradas em movimentações
2. **Vendas sem Movimentação de Estoque**: Itens de pedidos atendidos diretamente

## 🗺️ Navegação Específica - 4 Métodos Disponíveis

### 📍 Método 1: Via Página de Detalhes da Mola (Resumo)
**Melhor para**: Visualização rápida das últimas movimentações

#### Passo a Passo:
1. **Acesse a lista de molas**:
   - URL: `http://localhost:8000/molas/`
   - Menu: Molas

2. **Encontre a mola desejada**:
   - Use a busca ou navegue pela lista
   - Clique no **código da mola** (link azul)

3. **Visualize o histórico resumido**:
   - Na página de detalhes, localize a seção "Histórico de Movimentações"
   - Mostra as **últimas 10 movimentações**
   - Colunas: Data, Tipo, Quantidade, Ordem de Venda, Observação

4. **Para ver todas as movimentações**:
   - Clique no botão **"Ver Todas"** no canto superior direito da seção

#### Informações Exibidas:
- Data e hora da movimentação
- Tipo: Entrada (verde) ou Saída (vermelho)
- Quantidade movimentada
- Número da ordem de venda (se disponível)
- Observações da movimentação

### 📍 Método 2: Via Lista Completa de Movimentações (Detalhado)
**Melhor para**: Análise completa com filtros e exportação

#### Passo a Passo:
1. **Acesse movimentações**:
   - URL: `http://localhost:8000/movimentacoes/`
   - Menu: Movimentações

2. **Filtre por mola específica**:
   - Use o filtro "Mola" no topo da página
   - Selecione a mola desejada
   - Clique em "Filtrar"

3. **Filtre apenas vendas (opcional)**:
   - No filtro "Tipo", selecione "Saída"
   - Isso mostrará apenas as vendas

4. **Analise os resultados**:
   - Lista completa de todas as movimentações
   - Ordenação por data (mais recente primeiro)
   - Paginação para grandes volumes

#### URL Direta:
```
http://localhost:8000/movimentacoes/?mola=[ID_DA_MOLA]&tipo=S
```

### 📍 Método 3: Via Relatório Específico de Vendas por Mola (Mais Completo)
**Melhor para**: Análise detalhada incluindo vendas sem movimentação de estoque

#### Passo a Passo:
1. **Acesse o relatório**:
   - URL: `http://localhost:8000/relatorios/vendas-por-mola/`
   - Menu: Relatórios > Vendas por Mola

2. **Selecione a mola**:
   - No formulário, escolha a mola desejada no dropdown
   - Molas são ordenadas por cliente e código

3. **Escolha o formato**:
   - **HTML**: Visualização na tela
   - **PDF**: Download do relatório
   - **CSV**: Exportação para planilha

4. **Clique em "Gerar Relatório"**

#### Informações Exibidas:
- Data da venda
- Tipo: "Pedido" ou "Movimentação de Estoque"
- Número do pedido (quando aplicável)
- Quantidade vendida
- Observações detalhadas

### 📍 Método 4: Via Relatório de Molas Mais Vendidas (Contextual)
**Melhor para**: Encontrar mola no ranking e acessar detalhes

#### Passo a Passo:
1. **Acesse o relatório**:
   - URL: `http://localhost:8000/relatorios/molas-mais-vendidas/`
   - Menu: Relatórios > Molas Mais Vendidas

2. **Configure o período**:
   - Selecione período desejado (semana, mês, etc.)
   - Ou use período personalizado

3. **Encontre a mola**:
   - Localize a mola na tabela de resultados
   - Use Ctrl+F para buscar pelo código

4. **Acesse detalhes**:
   - Clique no código da mola para ir aos detalhes
   - Ou use os links de ação na coluna "Ações"

## 🔧 Funcionalidades Relacionadas

### 📅 Filtrar por Período Específico

#### No Relatório de Vendas por Mola:
- Atualmente não há filtro de período específico
- Mostra todo o histórico da mola

#### Na Lista de Movimentações:
- Use os filtros de "Data Inicial" e "Data Final"
- Combine com filtro de mola específica

#### No Relatório de Molas Mais Vendidas:
- Configure período antes de gerar
- Depois acesse detalhes da mola específica

### 📤 Exportar Dados

#### Formato PDF:
1. No relatório de vendas por mola, selecione "PDF"
2. Download automático do arquivo

#### Formato CSV:
1. No relatório de vendas por mola, selecione "CSV"
2. Abra em Excel ou Google Sheets

#### Formato HTML:
1. Visualize na tela
2. Use Ctrl+A e Ctrl+C para copiar
3. Cole em documento ou planilha

### 🔄 Vendas sem Movimentação de Estoque

#### Como Identificar:
- No relatório de vendas por mola, procure por:
  - Tipo: "Pedido"
  - Essas são vendas que não movimentaram estoque

#### Como Funciona:
- Itens de pedido com `movimentar_estoque=False`
- Aparecem no relatório de vendas por mola
- Não aparecem nas movimentações de estoque

## 📖 Localização no Manual

### Seção Específica:
**Arquivo**: `docs/manual_do_sistema.md`
**Seção**: "Localização de Funcionalidades > Visualizar Vendas de Uma Mola Específica"
**Linhas**: 1034-1045

### Conteúdo Documentado:
```markdown
#### Visualizar Vendas de Uma Mola Específica

1. **Método 1 - Via Lista de Molas**:
   - Acesse `/molas/`
   - Clique no código da mola desejada
   - Na página de detalhes, veja "Histórico de Movimentações"
   - Filtre por tipo "Saída" para ver apenas vendas

2. **Método 2 - Via Relatórios**:
   - Acesse `/relatorios/molas-mais-vendidas/`
   - Use filtros para encontrar a mola específica
   - Clique em "Detalhes" para análise completa
```

## 🧪 Instruções Testáveis

### Teste 1: Verificar Histórico via Detalhes da Mola
```bash
1. Abra http://localhost:8000/molas/
2. Clique em qualquer código de mola (link azul)
3. Procure a seção "Histórico de Movimentações"
4. Verifique se mostra últimas 10 movimentações
5. Clique em "Ver Todas" para lista completa
```

### Teste 2: Usar Relatório Específico
```bash
1. Abra http://localhost:8000/relatorios/vendas-por-mola/
2. Selecione uma mola no dropdown
3. Escolha formato "HTML"
4. Clique "Gerar Relatório"
5. Verifique se mostra vendas com e sem movimentação de estoque
```

### Teste 3: Filtrar Movimentações
```bash
1. Abra http://localhost:8000/movimentacoes/
2. No filtro "Mola", selecione uma mola específica
3. No filtro "Tipo", selecione "Saída"
4. Clique "Filtrar"
5. Verifique se mostra apenas vendas da mola selecionada
```

## ⚠️ Limitações Identificadas

### 🔴 Não Disponível Atualmente:
1. **Cliente por venda individual**: Sistema mostra apenas cliente da mola
2. **Filtro de período no relatório específico**: Mostra todo o histórico
3. **Preço por venda**: Não rastreado nas movimentações

### 🟡 Funcionalidades Parciais:
1. **Número do pedido**: Disponível apenas quando informado na movimentação
2. **Observações**: Dependem do preenchimento manual

### 🟢 Totalmente Funcional:
1. **Datas e quantidades**: Sempre disponíveis
2. **Tipos de movimentação**: Claramente identificados
3. **Exportação**: PDF e CSV funcionais
4. **Histórico completo**: Inclui vendas com e sem movimentação de estoque

## 🎯 Recomendações de Uso

### Para Análise Rápida:
- Use **Método 1** (Detalhes da Mola)

### Para Análise Detalhada:
- Use **Método 3** (Relatório Específico)

### Para Análise com Filtros:
- Use **Método 2** (Lista de Movimentações)

### Para Contexto de Ranking:
- Use **Método 4** (Molas Mais Vendidas)

---

**Última atualização**: Dezembro 2024  
**Versão do Sistema**: 2.2  
**Status**: ✅ Funcionalidade Totalmente Documentada
