@echo off
REM Script otimizado para iniciar o servidor com as correções aplicadas
REM Versão: 1.0 - Correções para erro "main thread is not in main loop"

echo.
echo ========================================
echo  SISTEMA DE CONTROLE DE ESTOQUE
echo  MOLAS RIOS - VERSAO CORRIGIDA
echo ========================================
echo.

REM Verificar se Python está instalado
python --version >nul 2>&1
if errorlevel 1 (
    echo ERRO: Python nao foi encontrado. Verifique a instalacao.
    pause
    exit /b 1
)

echo Python encontrado. Versao:
python --version
echo.

REM Definir variáveis de ambiente críticas para matplotlib
echo Configurando variaveis de ambiente...
set MPLBACKEND=Agg
set PYTHONOPTIMIZE=1
set PYTHONDONTWRITEBYTECODE=1

REM Variáveis de ambiente para Django
set DJANGO_DEBUG=True
set DJANGO_ALLOWED_HOSTS=localhost,127.0.0.1

echo Variaveis configuradas:
echo - MPLBACKEND=%MPLBACKEND%
echo - DJANGO_DEBUG=%DJANGO_DEBUG%
echo.

REM Limpar caches e arquivos temporários
echo Limpando caches e arquivos temporarios...
for /d /r . %%d in (__pycache__) do @if exist "%%d" rd /s /q "%%d" 2>nul
del /s /q *.pyc >nul 2>&1
echo Cache limpo.
echo.

REM Verificar se o banco de dados existe
if not exist "db.sqlite3" (
    echo Banco de dados nao encontrado. Executando migracoes...
    python manage.py makemigrations
    python manage.py migrate
    echo.
)

REM Otimizar banco de dados
echo Otimizando banco de dados...
python -c "import sqlite3; conn = sqlite3.connect('db.sqlite3'); conn.execute('VACUUM'); conn.close();" 2>nul
echo Banco otimizado.
echo.

REM Criar diretórios necessários
echo Criando diretorios necessarios...
if not exist "logs" mkdir logs
if not exist "media" mkdir media
if not exist "static" mkdir static
if not exist "staticfiles" mkdir staticfiles
echo Diretorios criados.
echo.

REM Testar configuração do matplotlib
echo Testando configuracao do matplotlib...
python -c "import os; os.environ['MPLBACKEND']='Agg'; import matplotlib; matplotlib.use('Agg', force=True); import matplotlib.pyplot as plt; plt.ioff(); print('Matplotlib configurado corretamente!')" 2>nul
if errorlevel 1 (
    echo AVISO: Problema na configuracao do matplotlib. Continuando...
) else (
    echo Matplotlib configurado com sucesso!
)
echo.

REM Verificar se a porta 8000 está livre
echo Verificando disponibilidade da porta 8000...
netstat -an | find "127.0.0.1:8000" >nul
if not errorlevel 1 (
    echo AVISO: Porta 8000 ja esta em uso. O servidor pode falhar ao iniciar.
    echo Pressione qualquer tecla para continuar ou Ctrl+C para cancelar.
    pause >nul
)

echo.
echo ========================================
echo  INICIANDO SERVIDOR DJANGO
echo ========================================
echo.
echo Acesse o sistema em: http://127.0.0.1:8000/
echo.
echo Para parar o servidor, pressione Ctrl+C
echo.

REM Iniciar o servidor com configurações otimizadas
python manage.py runserver --settings=controle_estoque.settings

echo.
echo Servidor encerrado.
pause
