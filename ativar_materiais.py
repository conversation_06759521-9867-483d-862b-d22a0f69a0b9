import os
import django

# Configurar o ambiente Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'controle_estoque.settings')
django.setup()

# Importar os modelos após configurar o ambiente
from estoque.models import Material

def ativar_materiais():
    """Ativa todos os materiais no banco de dados"""
    print("Verificando materiais...")
    
    # Contar materiais
    total_materiais = Material.objects.count()
    materiais_ativos = Material.objects.filter(ativo=True).count()
    materiais_inativos = Material.objects.filter(ativo=False).count()
    
    print(f"Total de materiais: {total_materiais}")
    print(f"Materiais ativos: {materiais_ativos}")
    print(f"Materiais inativos: {materiais_inativos}")
    
    # Se não houver materiais ativos, ativar todos
    if materiais_ativos == 0 and total_materiais > 0:
        print("Ativando todos os materiais...")
        
        # Atualizar todos os materiais para ativo=True
        materiais_atualizados = Material.objects.update(ativo=True)
        
        print(f"{materiais_atualizados} materiais foram ativados com sucesso!")
    elif materiais_ativos > 0:
        print("Já existem materiais ativos no sistema. Nenhuma ação necessária.")
    else:
        print("Não há materiais cadastrados no sistema.")
    
    # Listar todos os materiais
    print("\nLista de materiais:")
    for material in Material.objects.all():
        print(f"ID: {material.id}, Nome: {material}, Ativo: {material.ativo}")

if __name__ == "__main__":
    ativar_materiais()
