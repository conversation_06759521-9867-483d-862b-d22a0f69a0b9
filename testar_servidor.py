#!/usr/bin/env python
"""
Script para testar o servidor e verificar se os problemas foram corrigidos.
"""
import os
import sys
import subprocess
import time
import requests
from datetime import datetime

def testar_configuracao_matplotlib():
    """Testa se o matplotlib está configurado corretamente."""
    print("🔧 Testando configuração do matplotlib...")
    
    try:
        # Configurar backend antes de importar
        os.environ['MPLBACKEND'] = 'Agg'
        
        import matplotlib
        matplotlib.use('Agg', force=True)
        
        import matplotlib.pyplot as plt
        plt.ioff()
        
        # Testar criação de gráfico simples
        fig, ax = plt.subplots(figsize=(4, 3))
        ax.plot([1, 2, 3], [1, 4, 2])
        ax.set_title('Teste')
        
        # Salvar em buffer
        import io
        buffer = io.BytesIO()
        plt.savefig(buffer, format='png', dpi=150)
        buffer.seek(0)
        
        plt.close(fig)
        plt.clf()
        
        print("✅ Matplotlib configurado corretamente!")
        return True
        
    except Exception as e:
        print(f"❌ Erro na configuração do matplotlib: {e}")
        return False

def testar_imports_django():
    """Testa se os imports do Django estão funcionando."""
    print("🔧 Testando imports do Django...")
    
    try:
        # Configurar Django
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'controle_estoque.settings')
        
        import django
        django.setup()
        
        # Testar imports críticos
        from estoque.models import Mola, Material, MovimentacaoEstoque
        from estoque.views import gerar_pdf_molas_mais_vendidas
        from estoque.report_utils import create_chart, create_line_chart
        
        print("✅ Imports do Django funcionando corretamente!")
        return True
        
    except Exception as e:
        print(f"❌ Erro nos imports do Django: {e}")
        return False

def verificar_servidor_rodando():
    """Verifica se o servidor está rodando."""
    print("🔧 Verificando se o servidor está rodando...")
    
    try:
        response = requests.get('http://127.0.0.1:8000/', timeout=5)
        if response.status_code == 200:
            print("✅ Servidor está rodando!")
            return True
        else:
            print(f"⚠️ Servidor respondeu com status {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Servidor não está rodando")
        return False
    except Exception as e:
        print(f"❌ Erro ao verificar servidor: {e}")
        return False

def iniciar_servidor_teste():
    """Inicia o servidor em modo de teste."""
    print("🚀 Iniciando servidor em modo de teste...")
    
    try:
        # Comando para iniciar o servidor
        cmd = [sys.executable, 'manage.py', 'runserver', '--noreload']
        
        # Iniciar processo em background
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Aguardar um pouco para o servidor iniciar
        time.sleep(3)
        
        # Verificar se o processo ainda está rodando
        if process.poll() is None:
            print("✅ Servidor iniciado com sucesso!")
            return process
        else:
            stdout, stderr = process.communicate()
            print(f"❌ Servidor falhou ao iniciar:")
            print(f"STDOUT: {stdout}")
            print(f"STDERR: {stderr}")
            return None
            
    except Exception as e:
        print(f"❌ Erro ao iniciar servidor: {e}")
        return None

def testar_relatorio_pdf():
    """Testa a geração de relatório PDF."""
    print("🔧 Testando geração de relatório PDF...")
    
    try:
        # Fazer requisição para gerar relatório
        url = 'http://127.0.0.1:8000/relatorios/molas-mais-vendidas/'
        data = {
            'periodo': 'mes',
            'formato': 'pdf'
        }
        
        response = requests.post(url, data=data, timeout=30)
        
        if response.status_code == 200 and response.headers.get('content-type') == 'application/pdf':
            print("✅ Relatório PDF gerado com sucesso!")
            return True
        else:
            print(f"❌ Erro ao gerar relatório PDF: Status {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Erro ao testar relatório PDF: {e}")
        return False

def main():
    """Função principal do teste."""
    print("=" * 60)
    print("🧪 TESTE DE CORREÇÕES DO SISTEMA DE CONTROLE DE ESTOQUE")
    print("=" * 60)
    print(f"Data/Hora: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
    print()
    
    # Lista de testes
    testes = [
        ("Configuração do Matplotlib", testar_configuracao_matplotlib),
        ("Imports do Django", testar_imports_django),
    ]
    
    resultados = []
    
    # Executar testes básicos
    for nome, funcao in testes:
        print(f"📋 {nome}...")
        resultado = funcao()
        resultados.append((nome, resultado))
        print()
    
    # Verificar se servidor já está rodando
    servidor_rodando = verificar_servidor_rodando()
    print()
    
    # Se servidor não estiver rodando, tentar iniciar
    process = None
    if not servidor_rodando:
        process = iniciar_servidor_teste()
        if process:
            # Aguardar servidor estabilizar
            time.sleep(5)
            servidor_rodando = verificar_servidor_rodando()
    
    # Testar relatório PDF se servidor estiver rodando
    if servidor_rodando:
        print()
        resultado_pdf = testar_relatorio_pdf()
        resultados.append(("Geração de Relatório PDF", resultado_pdf))
    
    # Parar servidor se foi iniciado por este script
    if process and process.poll() is None:
        print("\n🛑 Parando servidor de teste...")
        process.terminate()
        process.wait()
    
    # Resumo dos resultados
    print("\n" + "=" * 60)
    print("📊 RESUMO DOS TESTES")
    print("=" * 60)
    
    todos_ok = True
    for nome, resultado in resultados:
        status = "✅ PASSOU" if resultado else "❌ FALHOU"
        print(f"{nome}: {status}")
        if not resultado:
            todos_ok = False
    
    print()
    if todos_ok:
        print("🎉 TODOS OS TESTES PASSARAM! Sistema corrigido com sucesso.")
    else:
        print("⚠️ ALGUNS TESTES FALHARAM. Verifique os erros acima.")
    
    print("=" * 60)

if __name__ == '__main__':
    main()
