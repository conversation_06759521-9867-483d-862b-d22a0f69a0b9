{% extends 'estoque/base.html' %}

{% block title %}Excluir Grupo de Previsões - Controle de Estoque{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Excluir Grupo de Previsões</h1>
        <div>
            <a href="{% url 'previsao-demanda-list' %}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Voltar
            </a>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header py-3 bg-danger text-white">
                    <h6 class="m-0 font-weight-bold">
                        <i class="fas fa-exclamation-triangle"></i> Confirmação de Exclusão
                    </h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-warning"></i>
                        <strong>Atenção!</strong> Esta ação não pode ser desfeita.
                    </div>

                    <p>Tem certeza que deseja excluir este grupo de previsões?</p>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <strong>ID do Grupo:</strong> {{ grupo_sessao }}
                        </div>
                        <div class="col-md-6">
                            <strong>Total de Previsões:</strong> {{ total_previsoes }}
                        </div>
                    </div>

                    <h6>Previsões que serão excluídas:</h6>
                    <div class="table-responsive" style="max-height: 300px; overflow-y: auto;">
                        <table class="table table-sm table-bordered">
                            <thead>
                                <tr>
                                    <th>Mola</th>
                                    <th>Cliente</th>
                                    <th>Quantidade Prevista</th>
                                    <th>Período</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for previsao in previsoes %}
                                    <tr>
                                        <td>{{ previsao.mola.codigo }}</td>
                                        <td>{{ previsao.mola.cliente }}</td>
                                        <td>{{ previsao.quantidade_prevista }}</td>
                                        <td>{{ previsao.get_periodo_display }}</td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <div class="d-flex justify-content-end mt-4">
                        <a href="{% url 'previsao-demanda-list' %}" class="btn btn-secondary me-2">
                            <i class="fas fa-times"></i> Cancelar
                        </a>
                        <form method="post" style="display: inline;">
                            {% csrf_token %}
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-trash"></i> Confirmar Exclusão
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
