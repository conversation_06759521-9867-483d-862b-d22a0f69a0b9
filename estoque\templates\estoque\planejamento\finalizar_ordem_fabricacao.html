{% extends 'estoque/base.html' %}

{% block title %}Finalizar Ordem de Fabricação - Molas Rios{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="mt-4">Finalizar Ordem de Fabricação</h1>
        <div>
            <a href="{% url 'planejamento-detail' planejamento.id %}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Voltar
            </a>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Detalhes da Ordem de Fabricação</h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <p><strong>Ordem:</strong> {{ planejamento.nome }}</p>
                    <p><strong>Data de Emissão:</strong>
                        {% if planejamento.status == 'P' %}
                            -
                        {% else %}
                            {{ planejamento.data_inicio|date:"d/m/Y" }}
                        {% endif %}
                    </p>
                    <p><strong>Status:</strong>
                        {% if planejamento.status == 'P' %}
                            <span class="badge bg-primary">Pendente</span>
                        {% elif planejamento.status == 'E' %}
                            <span class="badge bg-info">Em Produção</span>
                        {% elif planejamento.status == 'C' %}
                            <span class="badge bg-success">Concluído</span>
                        {% elif planejamento.status == 'A' %}
                            <span class="badge bg-warning">Atrasado</span>
                        {% elif planejamento.status == 'X' %}
                            <span class="badge bg-danger">Cancelado</span>
                        {% endif %}
                    </p>
                </div>
                <div class="col-md-6">
                    <p><strong>Mola:</strong> {{ item.mola.codigo }}</p>
                    <p><strong>Material:</strong>
                        {% if planejamento.material %}
                            {{ planejamento.material.nome }} - {{ planejamento.material.diametro }}
                        {% elif item.mola.material %}
                            {{ item.mola.material.nome }} - {{ item.mola.material.diametro }}
                        {% else %}
                            Não especificado
                        {% endif %}
                    </p>
                    <p><strong>Quantidade a Produzir:</strong> {{ item.quantidade }}</p>
                </div>
            </div>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Finalizar Produção</h6>
        </div>
        <div class="card-body">
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i> Informe a quantidade de molas produzidas que serão destinadas para venda direta e para estoque.
            </div>

            <form method="post">
                {% csrf_token %}

                {% if form.non_field_errors %}
                    <div class="alert alert-danger">
                        {% for error in form.non_field_errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                {% endif %}

                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="{{ form.quantidade_produzida.id_for_label }}" class="form-label">{{ form.quantidade_produzida.label }}</label>
                            {{ form.quantidade_produzida }}
                            {% if form.quantidade_produzida.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.quantidade_produzida.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">Quantidade total produzida</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="{{ form.quantidade_estoque_sem_revenir.id_for_label }}" class="form-label">{{ form.quantidade_estoque_sem_revenir.label }}</label>
                            {{ form.quantidade_estoque_sem_revenir }}
                            {% if form.quantidade_estoque_sem_revenir.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.quantidade_estoque_sem_revenir.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">{{ form.quantidade_estoque_sem_revenir.help_text }}</div>
                        </div>
                    </div>
                </div>

                <div class="alert alert-warning">
                    <h6><i class="fas fa-exclamation-triangle"></i> Importante:</h6>
                    <ul class="mb-0">
                        <li>A quantidade restante (Produzida - Sem Revenir) será adicionada ao estoque normal.</li>
                        <li>A quantidade para estoque sem revenir é opcional e pode ser zero.</li>
                        <li>O material será descontado automaticamente baseado na quantidade produzida.</li>
                    </ul>
                </div>

                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-check-circle"></i> Finalizar Ordem
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const quantidadeProduzida = document.getElementById('{{ form.quantidade_produzida.id_for_label }}');
        const quantidadeSemRevenir = document.getElementById('{{ form.quantidade_estoque_sem_revenir.id_for_label }}');

        // Focar no campo de quantidade produzida ao carregar a página
        quantidadeProduzida.focus();

        // Validação em tempo real
        function validarQuantidades() {
            const produzida = parseInt(quantidadeProduzida.value) || 0;
            const semRevenir = parseInt(quantidadeSemRevenir.value) || 0;

            if (semRevenir > produzida) {
                quantidadeSemRevenir.setCustomValidity('A quantidade para estoque sem revenir não pode ser maior que a quantidade produzida');
            } else {
                quantidadeSemRevenir.setCustomValidity('');
            }
        }

        quantidadeProduzida.addEventListener('input', validarQuantidades);
        quantidadeSemRevenir.addEventListener('input', validarQuantidades);

        // Permitir navegação com Enter
        quantidadeProduzida.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                quantidadeSemRevenir.focus();
            }
        });
    });
</script>
{% endblock %}
