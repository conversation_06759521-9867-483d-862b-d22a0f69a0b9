RELATÓRIO DE SEGURANÇA - 2025-06-17 08:56:48

================================================================================
VERIFICAÇÃO DA SECRET_KEY
================================================================================
OK: DJANGO_SECRET_KEY encontrada no arquivo .env.
OK: SECRET_KEY não está hardcoded no arquivo settings.py.


================================================================================
VERIFICAÇÃO DO MODO DEBUG
================================================================================
ALERTA: DJANGO_DEBUG está ativado no arquivo .env.
Recomendação: Defina DJANGO_DEBUG=False no arquivo .env para produção.
OK: DEBUG não está hardcoded como True no arquivo settings.py.


================================================================================
VERIFICAÇÃO DE ALLOWED_HOSTS
================================================================================
OK: DJANGO_ALLOWED_HOSTS está configurado no arquivo .env.
OK: ALLOWED_HOSTS não está hardcoded como ['*'] no arquivo settings.py.


================================================================================
VERIFICAÇÃO DE CONFIGURAÇÕES HTTPS
================================================================================
ALERTA: SECURE_SSL_REDIRECT está desativado no arquivo .env.
Recomendação: Defina SECURE_SSL_REDIRECT=True no arquivo .env para produção.
ALERTA: SESSION_COOKIE_SECURE está desativado no arquivo .env.
Recomendação: Defina SESSION_COOKIE_SECURE=True no arquivo .env para produção.
ALERTA: CSRF_COOKIE_SECURE está desativado no arquivo .env.
Recomendação: Defina CSRF_COOKIE_SECURE=True no arquivo .env para produção.
ALERTA: SECURE_HSTS_SECONDS está definido como 0, o que desativa HSTS.
Recomendação: Defina SECURE_HSTS_SECONDS=31536000 (1 ano) no arquivo .env para produção.


Relatório completo salvo em: C:\Users\<USER>\Documents\augment-projects\Controle de Estoque\relatorio_seguranca.txt
