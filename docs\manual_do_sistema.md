# Manual Completo do Sistema de Controle de Estoque - Molas Rios

## Índice

1. [Introdução](#introdução)
2. [Iniciando o Sistema](#iniciando-o-sistema)
3. [Navegação e Interface](#navegação-e-interface)
4. [Hierarquia Material Padrão → Variantes](#hierarquia-material-padrão--variantes)
5. [<PERSON><PERSON><PERSON><PERSON>](#módulos-principais)
   - [Dashboard](#dashboard)
   - [Cadastro de Materiais Padrão](#cadastro-de-materiais-padrão)
   - [Cadastro de Materiais (Variantes)](#cadastro-de-materiais-variantes)
   - [Cadastro de Molas](#cadastro-de-molas)
   - [Movimentação de Estoque](#movimentação-de-estoque)
   - [Pedidos de Venda](#pedidos-de-venda)
   - [Ordens de Fabricação](#ordens-de-fabricação)
   - [Previsão de Demanda](#previsão-de-demanda)
   - [Análise de Obsolescência](#análise-de-obsolescência)
   - [Relatórios](#relatórios)
6. [Cálculos e Algoritmos](#cálculos-e-algoritmos)
7. [Status e Fluxos de Trabalho](#status-e-fluxos-de-trabalho)
8. [Atalhos de Teclado](#atalhos-de-teclado)
9. [Localização de Funcionalidades](#localização-de-funcionalidades)
10. [Scripts e Ferramentas](#scripts-e-ferramentas)
11. [Solução de Problemas](#solução-de-problemas)

## Introdução

O Sistema de Controle de Estoque da Molas Rios é uma aplicação web completa desenvolvida em Django para gerenciar todos os aspectos do negócio de fabricação e venda de molas. O sistema oferece controle total sobre estoque, produção, vendas, previsão de demanda e análise de obsolescência.

### Características Principais

- **Interface em Português**: Todo o sistema está em português brasileiro
- **Tema Escuro**: Interface com cores roxo/lilás no tema escuro
- **Navegação por Teclado**: Suporte completo a atalhos de teclado
- **Relatórios Avançados**: PDFs elaborados com gráficos e análises
- **Hierarquia de Materiais**: Sistema de Material Padrão → Variantes
- **Controle de Produção**: Ordens de fabricação com status detalhados
- **Análise Preditiva**: Previsão de demanda e análise de obsolescência

### Conceitos Fundamentais

1. **Material Padrão**: Categoria base de material (ex: "Aço Carbono", diâmetro "6mm")
2. **Material Variante**: Variação específica do material padrão com características únicas
3. **Mola**: Produto final associado a um Material Padrão e opcionalmente a uma variante
4. **Ordem de Fabricação**: Processo de produção exclusivo para uma única mola
5. **Movimentação**: Entrada ou saída de estoque (manual ou automática)

## Iniciando o Sistema

### Requisitos do Sistema

- Python 3.8 ou superior
- Django 5.2
- Banco de dados SQLite (padrão) ou PostgreSQL
- Navegador web moderno

### Iniciando o Servidor

1. **Via Script Automático** (Recomendado):
   ```bash
   python iniciar_sistema.py
   ```

2. **Via Comando Django**:
   ```bash
   python manage.py runserver
   ```

3. **Acesso ao Sistema**:
   - URL: `http://localhost:8000`
   - O sistema abrirá automaticamente no navegador padrão

### Primeira Configuração

1. **Criar Superusuário** (se necessário):
   ```bash
   python manage.py createsuperuser
   ```

2. **Aplicar Migrações**:
   ```bash
   python manage.py migrate
   ```

3. **Carregar Dados Iniciais** (opcional):
   ```bash
   python manage.py loaddata dados_iniciais.json
   ```

## Navegação e Interface

### Estrutura da Interface

A interface do sistema é organizada em:

1. **Barra de Navegação Superior**: Links principais para todos os módulos
2. **Menu Lateral**: Navegação contextual baseada na seção atual
3. **Área Principal**: Conteúdo da página atual
4. **Rodapé**: Informações do sistema e links úteis

### URLs Principais

| Funcionalidade | URL Principal | URLs Alternativas |
|----------------|---------------|-------------------|
| Dashboard | `/` | `/dashboard/` |
| Molas | `/molas/` | - |
| Materiais Padrão | `/materiais-padrao/` | - |
| Materiais | `/materiais/` | - |
| Movimentações | `/movimentacoes/` | - |
| Pedidos | `/pedidos/` | - |
| Ordens de Fabricação | `/planejamento/` | `/ordens-fabricacao/` |
| Relatórios | `/relatorios/` | - |
| Previsão de Demanda | `/previsao-demanda/` | - |
| Análise de Obsolescência | `/analise-obsolescencia/` | - |

### Navegação por Breadcrumbs

O sistema utiliza breadcrumbs para mostrar a localização atual:
```
Home > Molas > Detalhes da Mola > Editar
```

## Hierarquia Material Padrão → Variantes

### Conceito

O sistema implementa uma hierarquia onde:

1. **Material Padrão**: Define a categoria base (nome + diâmetro)
   - Exemplo: "Aço Carbono" com diâmetro "6mm"

2. **Material Variante**: Especificações detalhadas do material padrão
   - Exemplo: "Aço Carbono SAE 1070 Temperado" (variante do "Aço Carbono 6mm")

3. **Mola**: Associada a um Material Padrão, opcionalmente a uma variante específica

### Como Funciona

1. **Cadastro de Material Padrão**:
   - Nome: "Aço Carbono"
   - Diâmetro: "6mm"

2. **Cadastro de Variantes**:
   - Material Padrão: "Aço Carbono 6mm"
   - Nome da Variante: "SAE 1070 Temperado"
   - Características específicas (fornecedor, preço, etc.)

3. **Cadastro de Mola**:
   - Material Padrão: "Aço Carbono 6mm"
   - Material (opcional): "SAE 1070 Temperado"

4. **Ordem de Fabricação**:
   - Ao selecionar uma mola, apenas variantes do seu Material Padrão aparecem
   - Facilita a seleção do material correto para produção

### Vantagens

- **Organização**: Materiais agrupados por categoria
- **Facilidade**: Seleção automática de materiais compatíveis
- **Consistência**: Evita erros na seleção de materiais
- **Flexibilidade**: Permite molas sem variante específica

## Módulos Principais

### Dashboard

O Dashboard é a página inicial do sistema, oferecendo uma visão geral completa do negócio.

#### Acesso
- **URL**: `/` ou `/dashboard/`
- **Atalho**: `Alt + H`

#### Funcionalidades

1. **Visão Geral**:
   - Total de molas cadastradas
   - Total de materiais em estoque
   - Pedidos pendentes
   - Ordens de fabricação ativas

2. **Alertas**:
   - Molas com estoque baixo
   - Materiais com estoque baixo
   - Pedidos atrasados
   - Ordens de fabricação vencidas

3. **Gráficos**:
   - Vendas por período
   - Produção por período
   - Status de estoque

4. **Dashboards Especializados**:
   - **Dashboard KPI** (`/dashboard/kpi/`): Indicadores de desempenho
   - **Dashboard Executivo** (`/dashboard/executivo/`): Visão estratégica
   - **Dashboard de Produção** (`/dashboard/producao/`): Foco na fabricação

### Cadastro de Materiais Padrão

Gerencia as categorias base de materiais.

#### Acesso
- **URL**: `/materiais-padrao/`
- **Menu**: Materiais > Materiais Padrão

#### Campos

1. **Nome**: Nome da categoria do material
   - Exemplo: "Aço Carbono", "Aço Inox", "Bronze"

2. **Diâmetro**: Diâmetro padrão do material
   - Exemplo: "6mm", "8mm", "10mm"

#### Funcionalidades

1. **Listar Materiais Padrão**:
   - Visualização em tabela
   - Ordenação por nome e diâmetro
   - Busca por nome ou diâmetro

2. **Criar Material Padrão**:
   - Formulário simples com nome e diâmetro
   - Validação de duplicatas

3. **Editar Material Padrão**:
   - Alteração de nome e diâmetro
   - Impacto em variantes associadas

4. **Excluir Material Padrão**:
   - Verificação de dependências
   - Confirmação obrigatória

#### Ordenação Automática

Todos os materiais padrão são ordenados automaticamente:
1. **Primeiro**: Por nome (ordem alfabética)
2. **Segundo**: Por diâmetro (ordem numérica crescente)

### Cadastro de Materiais (Variantes)

Gerencia as variantes específicas dos materiais padrão.

#### Acesso
- **URL**: `/materiais/`
- **Menu**: Materiais > Materiais
- **Atalho**: `Alt + N` (novo material)

#### Campos

1. **Material Padrão**: Categoria base (obrigatório)
2. **Nome**: Nome específico da variante
3. **Diâmetro**: Herdado do material padrão (não editável)
4. **Quantidade em Estoque**: Quantidade atual (kg)
5. **Estoque Mínimo**: Limite para alertas
6. **Fornecedor**: Nome do fornecedor
7. **Preço por Kg**: Custo do material
8. **Observações**: Informações adicionais
9. **Ativo**: Status do material

#### Funcionalidades

1. **Listar Materiais**:
   - Visualização em tabela com filtros
   - Indicação de estoque baixo
   - Status ativo/inativo

2. **Criar Material**:
   - Seleção de material padrão
   - Preenchimento automático do diâmetro
   - Validação de dados

3. **Editar Material**:
   - Alteração de todos os campos exceto material padrão
   - Histórico de alterações

4. **Movimentação de Material**:
   - Entrada de estoque
   - Saída de estoque
   - Histórico de movimentações

5. **Relatórios**:
   - Estoque atual
   - Movimentações por período
   - Materiais com estoque baixo

#### Cálculos Automáticos

- **Estoque Baixo**: Quando quantidade ≤ estoque mínimo
- **Valor Total**: Quantidade × preço por kg
- **Necessidade de Compra**: Baseada em ordens de fabricação

### Cadastro de Molas

Gerencia o catálogo de produtos (molas).

#### Acesso
- **URL**: `/molas/`
- **Menu**: Molas
- **Atalho**: `Alt + N` (nova mola)

#### Campos Principais

1. **Código**: Identificador único da mola
2. **Cliente**: Nome do cliente
3. **Material Padrão**: Categoria base do material (obrigatório)
4. **Material**: Variante específica (opcional)
5. **Diâmetro**: Opcional (pode usar do material padrão)
6. **Comprimento**: Comprimento da mola
7. **Peso Unitário**: Peso em gramas (precisão de 3 casas decimais)
8. **Quantidade por Volume**: Molas por embalagem
9. **Volumes**: Calculado automaticamente
10. **Quantidade em Estoque**: Estoque atual
11. **Estoque Mínimo**: Limite para alertas
12. **Observações**: Informações adicionais

#### Cálculos Automáticos

1. **Volumes**:
   ```
   volumes = quantidade_estoque ÷ quantidade_por_volume
   ```

2. **Peso Total**:
   ```
   peso_total = quantidade_estoque × peso_unitario
   ```

#### Funcionalidades

1. **Listar Molas**:
   - Tabela com filtros avançados
   - Busca por código, cliente, material
   - Indicação de estoque baixo
   - Coluna de volumes calculada

2. **Detalhes da Mola**:
   - Informações completas
   - Histórico de movimentações
   - Gráfico de vendas
   - Previsões de demanda

3. **Criar Mola**:
   - Formulário com validações
   - Cálculo automático de volumes
   - Seleção de material padrão obrigatória

4. **Editar Mola**:
   - Alteração de todos os campos
   - Recálculo automático de volumes
   - Histórico de alterações

5. **Movimentação**:
   - Entrada manual
   - Saída manual
   - Saída por venda
   - Entrada por produção

#### Navegação Específica

Para visualizar vendas específicas de uma mola:
1. Acesse `/molas/`
2. Clique no código da mola desejada
3. Na página de detalhes, veja a seção "Histórico de Movimentações"
4. Filtre por tipo "Saída" para ver apenas vendas
5. Use o botão "Ver Relatório de Vendas" para análise detalhada

### Movimentação de Estoque

Controla entradas e saídas de molas no estoque.

#### Acesso
- **URL**: `/movimentacoes/`
- **Menu**: Movimentações
- **Atalhos**:
  - `Alt + E` (nova entrada)
  - `Alt + S` (nova saída)
  - `Alt + M` (movimentação múltipla)

#### Tipos de Movimentação

1. **Entrada (E)**:
   - Produção concluída
   - Ajuste de estoque
   - Devolução de cliente

2. **Saída (S)**:
   - Venda para cliente
   - Ajuste de estoque
   - Perda/avaria

#### Campos

1. **Mola**: Produto movimentado
2. **Tipo**: Entrada ou Saída
3. **Quantidade**: Quantidade movimentada
4. **Data**: Data da movimentação
5. **Observações**: Motivo ou detalhes
6. **Origem**: Pedido, ordem de fabricação, etc.

#### Funcionalidades

1. **Movimentação Simples**:
   - Uma mola por vez
   - Formulário básico
   - Validação de estoque (saídas)

2. **Movimentação Múltipla**:
   - Várias molas em uma operação
   - Interface otimizada
   - Navegação por Enter
   - Adição automática de linhas

3. **Histórico**:
   - Lista todas as movimentações
   - Filtros por período, tipo, mola
   - Exportação para CSV/PDF

4. **Relatórios**:
   - Movimentações por período
   - Análise de entradas/saídas
   - Gráficos de tendência

#### Navegação por Teclado (Movimentação Múltipla)

1. **Enter** no campo Cliente → move para Tipo
2. **Enter** no campo Tipo → move para Ordem de Venda
3. **Enter** no campo Ordem de Venda → move para Observação
4. **Ctrl + Enter** no campo Observação → move para primeira mola
5. **Enter** no campo Nome da Mola → move para Quantidade
6. **Enter** no campo Quantidade → adiciona nova linha ou próxima mola

### Pedidos de Venda

Gerencia pedidos de clientes e controla vendas.

#### Acesso
- **URL**: `/pedidos/`
- **Menu**: Pedidos
- **Atalho**: `Alt + N` (novo pedido)

#### Status de Pedidos

1. **Pendente**: Aguardando processamento
2. **Em Produção**: Itens sendo fabricados
3. **Concluído**: Pedido finalizado
4. **Cancelado**: Pedido cancelado

#### Campos do Pedido

1. **Cliente**: Nome do cliente
2. **Data do Pedido**: Data de criação
3. **Data de Entrega**: Prazo de entrega
4. **Status**: Status atual
5. **Observações**: Informações adicionais
6. **Valor Total**: Calculado automaticamente

#### Itens do Pedido

1. **Mola**: Produto solicitado
2. **Quantidade**: Quantidade pedida
3. **Preço Unitário**: Preço por unidade
4. **Valor Total**: Quantidade × preço unitário

#### Funcionalidades

1. **Criar Pedido**:
   - Dados básicos do pedido
   - Adição de múltiplos itens
   - Cálculo automático de valores

2. **Processar Pedido**:
   - Verificação de estoque
   - Criação automática de ordens de fabricação
   - Movimentação de estoque

3. **Adicionar/Remover Itens**:
   - Edição de itens existentes
   - Adição de novos itens
   - Remoção de itens específicos

4. **Relatórios**:
   - Pedidos por período
   - Análise de vendas
   - Performance por cliente

#### Fluxo de Trabalho

1. **Criação**: Cliente faz pedido
2. **Análise**: Verificação de estoque
3. **Produção**: Criação de ordens de fabricação (se necessário)
4. **Processamento**: Separação e envio
5. **Conclusão**: Finalização do pedido

### Ordens de Fabricação

Controla o processo de produção de molas. Cada ordem é exclusiva para uma única mola.

#### Acesso
- **URL**: `/planejamento/` ou `/ordens-fabricacao/`
- **Menu**: Produção > Ordens de Fabricação
- **Dashboard**: Dashboard de Produção (`/dashboard/producao/`)

#### Conceitos Importantes

- **Uma Ordem = Uma Mola**: Cada ordem de fabricação é específica para um único tipo de mola
- **Não há "Adicionar Item"**: Diferente de pedidos, ordens não permitem múltiplos itens
- **Material Filtrado**: Apenas variantes do Material Padrão da mola aparecem

#### Status das Ordens

1. **Pendente (P)**: Aguardando início da produção
2. **Em Andamento (A)**: Produção iniciada
3. **Concluída (C)**: Produção finalizada
4. **Cancelada (X)**: Ordem cancelada

#### Campos da Ordem

1. **Mola**: Produto a ser fabricado (obrigatório)
2. **Quantidade**: Quantidade a produzir
3. **Data de Início**: Data planejada/real de início
4. **Data de Conclusão**: Data planejada/real de conclusão
5. **Prioridade**: Alta, Média, Baixa
6. **Status**: Status atual da ordem
7. **Observações**: Informações adicionais

#### Necessidade de Materiais

O sistema calcula automaticamente a necessidade de materiais baseada em:

1. **Peso Unitário da Mola**: Peso em gramas
2. **Quantidade a Produzir**: Quantidade da ordem
3. **Fórmula**:
   ```
   necessidade_kg = (peso_unitario_gramas × quantidade) ÷ 1000
   ```

4. **Exibição**: Sempre mostrada com unidade "kg"

#### Funcionalidades

1. **Criar Ordem**:
   - Seleção da mola
   - Definição de quantidade e prazos
   - Cálculo automático de materiais

2. **Iniciar Produção**:
   - Botão "Produção Iniciada"
   - Muda status para "Em Andamento"
   - Registra data de início real

3. **Finalizar Ordem**:
   - Processo 100% automático
   - Adiciona quantidade ao estoque da mola
   - Desconta material selecionado automaticamente
   - Muda status para "Concluída"
   - **Não abre páginas adicionais**

4. **Seleção de Material**:
   - Lista apenas variantes do Material Padrão da mola
   - Verificação de estoque disponível
   - Desconto automático na finalização

5. **Relatórios**:
   - Ordens por período
   - Eficiência de produção
   - Consumo de materiais

#### Fluxo Completo

1. **Criação**:
   - Selecionar mola
   - Definir quantidade
   - Sistema calcula necessidade de material

2. **Planejamento**:
   - Definir datas
   - Estabelecer prioridade
   - Verificar disponibilidade de material

3. **Início**:
   - Clicar "Produção Iniciada"
   - Status muda para "Em Andamento"

4. **Produção**:
   - Processo físico de fabricação
   - Acompanhamento via dashboard

5. **Finalização**:
   - Clicar "Finalizar Ordem"
   - Sistema automaticamente:
     - Adiciona molas ao estoque
     - Desconta material usado
     - Marca ordem como concluída

### Previsão de Demanda

Analisa histórico de vendas e gera previsões para planejamento.

#### Acesso
- **URL**: `/previsao-demanda/`
- **Menu**: Análises > Previsão de Demanda

#### Métodos de Previsão

1. **Média Móvel**: Baseada em períodos anteriores
2. **Tendência Linear**: Análise de crescimento/declínio
3. **Sazonal**: Considera variações sazonais
4. **Avançado**: Usa algoritmos estatísticos (se disponível)

#### Períodos Disponíveis

1. **Próxima Semana**: Previsão para 7 dias
2. **Próximo Mês**: Previsão para 30 dias
3. **Próximos 3 Meses**: Previsão trimestral
4. **Próximos 6 Meses**: Previsão semestral

#### Funcionalidades

1. **Gerar Previsão Individual**:
   - Selecionar mola específica
   - Escolher período e método
   - Análise detalhada com gráficos

2. **Gerar Previsões em Lote**:
   - Todas as molas ativas
   - Método padrão
   - Processamento em background

3. **Análise de Precisão**:
   - Comparação com vendas reais
   - Cálculo de erro percentual
   - Ajuste de métodos

4. **Relatórios**:
   - Previsões por período
   - Análise de tendências
   - Recomendações de produção

#### Cálculos

1. **Média Móvel**:
   ```
   previsao = soma(vendas_ultimos_n_periodos) ÷ n_periodos
   ```

2. **Tendência Linear**:
   ```
   previsao = vendas_base + (tendencia × periodos_futuros)
   ```

3. **Precisão**:
   ```
   precisao = 100 - |((previsto - real) ÷ real) × 100|
   ```

### Análise de Obsolescência

Identifica produtos com baixa rotatividade ou sem movimentação.

#### Acesso
- **URL**: `/analise-obsolescencia/`
- **Menu**: Análises > Análise de Obsolescência

#### Classificações

1. **A - Alta Rotatividade**: Vendas frequentes
2. **B - Média Rotatividade**: Vendas regulares
3. **C - Baixa Rotatividade**: Vendas esporádicas
4. **D - Sem Movimentação**: Sem vendas recentes
5. **O - Obsoleto**: Sem vendas há muito tempo

#### Critérios de Classificação

1. **Dias sem Movimentação**:
   - A: 0-30 dias
   - B: 31-90 dias
   - C: 91-180 dias
   - D: 181-365 dias
   - O: >365 dias

2. **Valor do Estoque**: Impacto financeiro

3. **Histórico de Vendas**: Padrão de movimentação

#### Funcionalidades

1. **Gerar Análise**:
   - Processa todas as molas
   - Calcula classificações
   - Gera recomendações

2. **Resumo por Classificação**:
   - Contagem por categoria
   - Valor total por categoria
   - Percentuais do estoque

3. **Filtros**:
   - Por classificação
   - Por período sem movimentação
   - Por valor de estoque

4. **Relatórios**:
   - Análise completa em PDF
   - Lista de itens obsoletos
   - Recomendações de ação

#### Recomendações Automáticas

1. **Classificação A/B**: Manter estoque
2. **Classificação C**: Monitorar vendas
3. **Classificação D**: Considerar promoção
4. **Classificação O**: Avaliar descontinuação

### Relatórios

O sistema oferece relatórios avançados com design profissional e gráficos interativos.

#### Acesso
- **URL**: `/relatorios/`
- **Menu**: Relatórios

#### Padrão Visual dos Relatórios

Todos os relatórios seguem o mesmo padrão:

1. **Cabeçalho**:
   - Logo da empresa (Molas Rios)
   - Título do relatório
   - Data de geração

2. **Filtros Aplicados**: Informações sobre período e filtros

3. **Conteúdo Principal**: Tabelas e gráficos

4. **Rodapé**: Informações do sistema e numeração

#### Relatórios Disponíveis

##### 1. Molas Mais Vendidas

**Acesso**: `/relatorios/molas-mais-vendidas/`

**Funcionalidades**:
- Períodos predefinidos (semana, mês, 3 meses, 6 meses, ano)
- Período personalizado com seletor de datas
- Meses específicos não consecutivos
- Subtítulo personalizado opcional

**Dados Incluídos**:
- Posição no ranking
- Código e cliente da mola
- Quantidade vendida
- Média mensal de vendas
- Variação percentual vs. período anterior
- Comparação com média histórica
- Gráfico de tendência de vendas por mês

**Cálculos**:
```
media_mensal = total_vendido ÷ numero_meses_periodo
variacao_percentual = ((vendas_atual - vendas_anterior) ÷ vendas_anterior) × 100
variacao_media_historica = ((vendas_periodo - media_historica) ÷ media_historica) × 100
```

**Ordenação**: Por quantidade vendida (decrescente)

##### 2. Comparação entre Períodos

**Acesso**: `/relatorios/comparacao-periodos/`

**Funcionalidades**:
- Comparação entre dois períodos específicos
- Seletores de mês/ano ou datas personalizadas
- Subtítulo personalizado opcional
- Cálculo automático de variações

**Dados Incluídos**:
- Vendas em cada período
- Variação percentual
- Diferença absoluta
- Ordenação por vendas totais dos dois períodos

**Cálculos**:
```
variacao_percentual = ((valor_recente - valor_antigo) ÷ valor_antigo) × 100
diferenca_absoluta = vendas_periodo1 - vendas_periodo2
```

**Ordenação**: Por soma das vendas dos dois períodos (decrescente)

##### 3. Relatório de Estoque

**Acesso**: `/relatorios/estoque/`

**Funcionalidades**:
- Filtros por cliente, código, estoque baixo
- Múltiplas opções de ordenação
- Status de estoque automático

**Dados Incluídos**:
- Informações completas das molas
- Status de estoque (OK/Estoque Baixo)
- Estatísticas resumidas

##### 4. Análise de Obsolescência

**Acesso**: `/analise-obsolescencia/`

**Funcionalidades**:
- Classificação automática por rotatividade
- Resumo por classificação
- Recomendações específicas
- Formato vertical padronizado

**Dados Incluídos**:
- Classificação (A, B, C, D, O)
- Dias sem movimentação
- Última movimentação
- Valor do estoque
- Recomendações automáticas

#### Formatos de Exportação

1. **PDF**: Relatórios elaborados com gráficos
2. **CSV**: Dados tabulares para análise
3. **Visualização Web**: Interface interativa

#### Gráficos Incluídos

1. **Gráfico de Tendência**: Vendas mensais ao longo do tempo
2. **Gráfico de Barras**: Top molas por quantidade
3. **Gráfico de Pizza**: Distribuição por classificação (obsolescência)

## Cálculos e Algoritmos

### Cálculo de Volumes

```python
def calcular_volumes(quantidade_estoque, quantidade_por_volume):
    if quantidade_por_volume and quantidade_por_volume > 0:
        return quantidade_estoque // quantidade_por_volume
    return 0
```

### Cálculo de Necessidade de Material

```python
def calcular_necessidade_material(peso_unitario_gramas, quantidade_produzir):
    # Converte gramas para quilogramas
    peso_unitario_kg = peso_unitario_gramas / 1000
    necessidade_kg = peso_unitario_kg * quantidade_produzir
    return round(necessidade_kg, 3)
```

### Cálculo de Média Mensal

```python
def calcular_media_mensal(total_vendas, data_inicio, data_fim):
    # Calcula número de meses no período
    num_meses = ((data_fim.year - data_inicio.year) * 12 +
                 data_fim.month - data_inicio.month + 1)

    if num_meses > 0:
        return round(total_vendas / num_meses)
    return 0
```

### Cálculo de Variação Percentual

```python
def calcular_variacao_percentual(valor_atual, valor_anterior):
    if valor_anterior == 0:
        return 100 if valor_atual > 0 else 0

    variacao = ((valor_atual - valor_anterior) / valor_anterior) * 100
    return round(variacao, 1)
```

### Algoritmo de Classificação de Obsolescência

```python
def classificar_obsolescencia(dias_sem_movimentacao):
    if dias_sem_movimentacao <= 30:
        return 'A'  # Alta Rotatividade
    elif dias_sem_movimentacao <= 90:
        return 'B'  # Média Rotatividade
    elif dias_sem_movimentacao <= 180:
        return 'C'  # Baixa Rotatividade
    elif dias_sem_movimentacao <= 365:
        return 'D'  # Sem Movimentação
    else:
        return 'O'  # Obsoleto
```

### Ordenação de Materiais

```python
def ordenar_materiais(materiais):
    # Primeiro por nome (alfabética), depois por diâmetro (numérica)
    return sorted(materiais, key=lambda m: (
        m.nome.lower(),
        extrair_valor_numerico_diametro(m.diametro)
    ))
```

## Status e Fluxos de Trabalho

### Status de Pedidos

| Status | Cor | Descrição | Próximas Ações |
|--------|-----|-----------|----------------|
| Pendente | Amarelo | Aguardando processamento | Analisar estoque, iniciar produção |
| Em Produção | Azul | Itens sendo fabricados | Acompanhar ordens de fabricação |
| Concluído | Verde | Pedido finalizado | Arquivo, análise de satisfação |
| Cancelado | Vermelho | Pedido cancelado | Análise de motivos |

### Status de Ordens de Fabricação

| Status | Cor | Descrição | Ações Disponíveis |
|--------|-----|-----------|-------------------|
| Pendente | Amarelo | Aguardando início | Iniciar Produção |
| Em Andamento | Azul | Produção iniciada | Finalizar Ordem |
| Concluída | Verde | Produção finalizada | Visualizar apenas |
| Cancelada | Vermelho | Ordem cancelada | Visualizar apenas |

### Fluxo de Trabalho Completo

```mermaid
graph TD
    A[Pedido do Cliente] --> B{Estoque Disponível?}
    B -->|Sim| C[Processar Pedido]
    B -->|Não| D[Criar Ordem de Fabricação]
    D --> E[Iniciar Produção]
    E --> F[Finalizar Produção]
    F --> G[Atualizar Estoque]
    G --> C
    C --> H[Pedido Concluído]
```

### Alertas Automáticos

1. **Estoque Baixo**: Quando quantidade ≤ estoque mínimo
2. **Material Insuficiente**: Para ordens de fabricação
3. **Pedidos Atrasados**: Data de entrega vencida
4. **Ordens Vencidas**: Data de conclusão ultrapassada

## Atalhos de Teclado

O sistema oferece suporte completo a navegação por teclado para agilizar o trabalho.

### Atalhos Globais

| Atalho | Descrição | Contexto |
|--------|-----------|----------|
| `Alt + N` | Criar novo item | Qualquer página de listagem |
| `Alt + S` | Salvar formulário | Formulários |
| `Alt + B` | Voltar/Cancelar | Qualquer página |
| `Alt + H` | Ir para Dashboard | Global |
| `Alt + F` | Focar campo de busca | Páginas com busca |
| `Esc` | Fechar diálogos/alertas | Global |

### Navegação em Formulários

| Atalho | Descrição |
|--------|-----------|
| `Enter` | Avançar para próximo campo |
| `Shift + Enter` | Voltar para campo anterior |
| `Tab` | Navegação padrão (próximo) |
| `Shift + Tab` | Navegação padrão (anterior) |

### Movimentação de Estoque

| Atalho | Descrição |
|--------|-----------|
| `Alt + E` | Nova entrada de estoque |
| `Alt + S` | Nova saída de estoque |
| `Alt + M` | Movimentação múltipla |
| `Ctrl + Enter` | Adicionar item (movimentação múltipla) |

### Navegação Específica - Movimentação Múltipla

1. **Campos Principais**:
   - `Enter` em Cliente → Tipo
   - `Enter` em Tipo → Ordem de Venda
   - `Enter` em Ordem de Venda → Observação
   - `Ctrl + Enter` em Observação → Primeira mola

2. **Campos de Molas**:
   - `Enter` em Nome da Mola → Quantidade (mesma linha)
   - `Enter` em Quantidade → Nome da Mola (próxima linha ou nova linha)

### Pedidos de Venda

| Atalho | Descrição |
|--------|-----------|
| `Alt + P` | Novo pedido |
| `Alt + I` | Adicionar item ao pedido |
| `Alt + R` | Remover item selecionado |
| `Ctrl + P` | Processar pedido |

### Ordens de Fabricação

| Atalho | Descrição |
|--------|-----------|
| `Alt + O` | Nova ordem de fabricação |
| `Alt + I` | Iniciar produção |
| `Alt + F` | Finalizar ordem |
| `Alt + C` | Cancelar ordem |

### Relatórios

| Atalho | Descrição |
|--------|-----------|
| `Alt + R` | Gerar relatório |
| `Ctrl + P` | Exportar PDF |
| `Ctrl + E` | Exportar CSV |
| `F5` | Atualizar dados |

### Navegação em Listas

| Atalho | Descrição |
|--------|-----------|
| `↑` / `↓` | Navegar entre itens |
| `Home` | Primeiro item |
| `End` | Último item |
| `Page Up` | Página anterior |
| `Page Down` | Próxima página |
| `Enter` | Selecionar/Abrir item |

## Localização de Funcionalidades

### Como Encontrar Funcionalidades Específicas

#### Visualizar Vendas de Uma Mola Específica

**📋 Guia Completo**: Consulte `GUIA_HISTORICO_VENDAS_MOLA.md` para instruções detalhadas

**🎯 4 Métodos Disponíveis**:

1. **Método 1 - Via Detalhes da Mola** (Resumo):
   - Acesse `/molas/`
   - Clique no código da mola desejada
   - Veja "Histórico de Movimentações" (últimas 10)
   - Clique "Ver Todas" para lista completa

2. **Método 2 - Via Lista de Movimentações** (Detalhado):
   - Acesse `/movimentacoes/`
   - Filtre por mola específica e tipo "Saída"
   - URL direta: `/movimentacoes/?mola=[ID]&tipo=S`

3. **Método 3 - Via Relatório Específico** (Mais Completo):
   - Acesse `/relatorios/vendas-por-mola/`
   - Selecione a mola desejada
   - Inclui vendas com e sem movimentação de estoque
   - Exportação em PDF/CSV disponível

4. **Método 4 - Via Relatório de Ranking** (Contextual):
   - Acesse `/relatorios/molas-mais-vendidas/`
   - Configure período desejado
   - Encontre a mola no ranking
   - Clique no código para detalhes

**📊 Informações Disponíveis**:
- Datas e quantidades de cada venda
- Tipos: Movimentação de Estoque ou Pedido
- Números de pedidos (quando disponível)
- Observações detalhadas
- Vendas sem movimentação de estoque separadamente

#### Acompanhar Status de Produção

1. **Dashboard de Produção**: `/dashboard/producao/`
2. **Lista de Ordens**: `/planejamento/`
3. **Filtros por Status**: Pendente, Em Andamento, Concluída

#### Verificar Estoque Baixo

1. **Dashboard Principal**: Alertas na página inicial
2. **Relatório de Estoque**: `/relatorios/estoque/` com filtro "Estoque Baixo"
3. **Lista de Molas**: Indicação visual de estoque baixo

#### Analisar Tendências de Vendas

1. **Relatório Molas Mais Vendidas**: Gráfico de tendência
2. **Comparação entre Períodos**: Análise de variações
3. **Previsão de Demanda**: Projeções futuras

### Estrutura de Navegação

```
Dashboard (/)
├── Molas (/molas/)
│   ├── Nova Mola (/molas/nova/)
│   ├── Editar Mola (/molas/{id}/editar/)
│   └── Detalhes (/molas/{id}/)
├── Materiais
│   ├── Materiais Padrão (/materiais-padrao/)
│   └── Materiais (/materiais/)
├── Movimentações (/movimentacoes/)
│   ├── Nova Movimentação (/movimentacoes/nova/)
│   └── Movimentação Múltipla (/movimentacoes/multipla/)
├── Pedidos (/pedidos/)
│   ├── Novo Pedido (/pedidos/novo/)
│   └── Processar Pedido (/pedidos/{id}/processar/)
├── Produção
│   ├── Ordens de Fabricação (/planejamento/)
│   └── Dashboard de Produção (/dashboard/producao/)
├── Análises
│   ├── Previsão de Demanda (/previsao-demanda/)
│   └── Análise de Obsolescência (/analise-obsolescencia/)
└── Relatórios (/relatorios/)
    ├── Molas Mais Vendidas
    ├── Comparação entre Períodos
    ├── Relatório de Estoque
    └── Análise de Obsolescência
```

## Scripts e Ferramentas

### Scripts Disponíveis

#### 1. iniciar_sistema.py
**Função**: Inicia o servidor Django automaticamente
```bash
python iniciar_sistema.py
```
**Funcionalidades**:
- Verifica dependências
- Aplica migrações pendentes
- Inicia servidor na porta 8000
- Abre navegador automaticamente

#### 2. backup_sistema.py
**Função**: Cria backup completo do sistema
```bash
python backup_sistema.py
```
**Funcionalidades**:
- Backup do banco de dados
- Backup de arquivos de mídia
- Compactação automática
- Rotação de backups antigos

#### 3. restaurar_backup.py
**Função**: Restaura backup do sistema
```bash
python restaurar_backup.py nome_do_backup.zip
```
**Funcionalidades**:
- Restaura banco de dados
- Restaura arquivos de mídia
- Validação de integridade

#### 4. limpar_redundancias.py
**Função**: Remove arquivos duplicados e limpa sistema
```bash
python limpar_redundancias.py
```
**Funcionalidades**:
- Remove arquivos duplicados
- Limpa cache temporário
- Otimiza banco de dados

#### 5. gerar_relatorio_sistema.py
**Função**: Gera relatório completo do sistema
```bash
python gerar_relatorio_sistema.py
```
**Funcionalidades**:
- Estatísticas de uso
- Performance do sistema
- Relatório de erros

### Manutenção do Sistema

#### Backup Automático
O sistema pode ser configurado para backup automático:
1. Configure o agendador do sistema operacional
2. Execute `python backup_sistema.py` diariamente
3. Mantenha pelo menos 7 backups recentes

#### Limpeza Periódica
Execute mensalmente:
```bash
python limpar_redundancias.py
python manage.py clearsessions
```

#### Monitoramento
Verifique regularmente:
- Espaço em disco
- Performance do banco de dados
- Logs de erro
- Atualizações de segurança

## Solução de Problemas

### Problemas Comuns

#### 1. Sistema Não Inicia
**Sintomas**: Erro ao executar `python iniciar_sistema.py`

**Soluções**:
1. Verificar se Python está instalado
2. Ativar ambiente virtual: `venv\Scripts\activate`
3. Instalar dependências: `pip install -r requirements.txt`
4. Aplicar migrações: `python manage.py migrate`

#### 2. Erro de Banco de Dados
**Sintomas**: Erro de conexão ou tabelas não encontradas

**Soluções**:
1. Verificar arquivo `db.sqlite3`
2. Executar migrações: `python manage.py migrate`
3. Restaurar backup se necessário

#### 3. Relatórios Não Geram
**Sintomas**: Erro ao gerar PDF ou CSV

**Soluções**:
1. Verificar instalação do ReportLab
2. Verificar permissões de escrita
3. Limpar cache: `python manage.py clearcache`

#### 4. Performance Lenta
**Sintomas**: Sistema lento para carregar

**Soluções**:
1. Executar limpeza: `python limpar_redundancias.py`
2. Otimizar banco: `python manage.py optimize_db`
3. Verificar espaço em disco

#### 5. Atalhos de Teclado Não Funcionam
**Sintomas**: Teclas não respondem

**Soluções**:
1. Verificar se JavaScript está habilitado
2. Atualizar navegador
3. Limpar cache do navegador

### Logs e Diagnóstico

#### Localização dos Logs
- **Sistema**: `logs/sistema.log`
- **Erros**: `logs/erros.log`
- **Django**: `logs/django.log`

#### Níveis de Log
1. **DEBUG**: Informações detalhadas
2. **INFO**: Operações normais
3. **WARNING**: Situações de atenção
4. **ERROR**: Erros que precisam correção
5. **CRITICAL**: Erros críticos do sistema

### Contato e Suporte

Para problemas não resolvidos:
1. Consulte os logs do sistema
2. Verifique a documentação técnica
3. Execute diagnóstico: `python gerar_relatorio_sistema.py`

---

**Última atualização**: Dezembro 2024
**Versão do Manual**: 3.0
**Versão do Sistema**: 2.2

O Sistema de Controle de Estoque da Molas Rios é uma aplicação web desenvolvida para gerenciar o estoque de molas e materiais, controlar movimentações, gerar pedidos de venda, planejar a produção e realizar análises avançadas como previsão de demanda e análise de obsolescência.

### Requisitos do Sistema

- Windows 7 ou superior
- Python 3.8 ou superior
- Navegador web atualizado (Chrome, Firefox, Edge)
- Mínimo de 4GB de RAM
- 500MB de espaço em disco

## Iniciando o Sistema

### Primeira Execução

1. Execute o script `executar_gerar_secret_key.bat` para gerar uma chave de segurança
2. Execute o script `iniciar_servidor_seguro.bat` para iniciar o servidor
3. Abra o navegador e acesse: http://127.0.0.1:8000/

### Uso Diário

1. Execute o script `iniciar_servidor_seguro.bat`
2. Acesse o sistema no navegador: http://127.0.0.1:8000/
3. Para encerrar, pressione `Ctrl+C` na janela do servidor e confirme

## Módulos Principais

### Cadastro de Molas

O módulo de cadastro de molas permite gerenciar o catálogo de molas fabricadas pela empresa.

#### Funcionalidades

- **Listar Molas**: Visualize todas as molas cadastradas com filtros por código, cliente e status
- **Cadastrar Nova Mola**: Adicione uma nova mola ao sistema
- **Editar Mola**: Atualize informações de uma mola existente
- **Visualizar Detalhes**: Veja informações detalhadas, histórico de movimentações e estatísticas
- **Inativar/Reativar**: Marque molas como inativas sem excluí-las do sistema

#### Campos Importantes

- **Código**: Identificador único da mola
- **Nome da Mola**: Número da mola (após a '/' no código)
- **Cliente**: Nome do cliente para quem a mola é fabricada
- **Material**: Material utilizado na fabricação
- **Peso Unitário**: Peso da mola em gramas (medido manualmente)
- **Quantidade em Estoque**: Quantidade atual disponível
- **Estoque Mínimo**: Quantidade mínima desejada em estoque

### Cadastro de Materiais

O módulo de cadastro de materiais permite gerenciar os materiais utilizados na fabricação das molas.

#### Funcionalidades

- **Listar Materiais**: Visualize todos os materiais com filtros por nome, diâmetro e fornecedor
- **Cadastrar Novo Material**: Adicione um novo material ao sistema
- **Editar Material**: Atualize informações de um material existente
- **Visualizar Detalhes**: Veja informações detalhadas e histórico de movimentações
- **Inativar/Reativar**: Marque materiais como inativos sem excluí-los do sistema

#### Campos Importantes

- **Nome**: Nome do material
- **Diâmetro**: Diâmetro do material (ex: Ø 0.40 mm)
- **Quantidade em Estoque**: Quantidade atual disponível em kg
- **Estoque Mínimo**: Quantidade mínima desejada em estoque
- **Fornecedor**: Nome do fornecedor do material

### Movimentação de Estoque

O módulo de movimentação permite registrar entradas e saídas de molas e materiais do estoque.

#### Funcionalidades

- **Registrar Entrada**: Adicione molas ou materiais ao estoque
- **Registrar Saída**: Remova molas ou materiais do estoque
- **Movimentação Múltipla**: Registre várias movimentações de uma vez
- **Histórico**: Visualize o histórico completo de movimentações
- **Filtros**: Filtre movimentações por data, tipo, mola/material
- **Acesso Rápido**: Botões de ação direta nas listas de molas e materiais

#### Tipos de Movimentação

- **Entrada**: Aumenta a quantidade em estoque
- **Saída**: Diminui a quantidade em estoque

#### Acesso Rápido às Movimentações

Na lista de molas e materiais, você encontrará botões de ação rápida para:

- **Adicionar ao Estoque** (ícone +): Redireciona diretamente para o formulário de entrada com a mola/material já selecionado
- **Remover do Estoque** (ícone -): Redireciona diretamente para o formulário de saída com a mola/material já selecionado

Esses botões agilizam o processo de movimentação, eliminando a necessidade de selecionar manualmente a mola ou material no formulário de movimentação.

### Pedidos de Venda

O módulo de pedidos permite registrar e gerenciar pedidos de venda de molas.

#### Funcionalidades

- **Criar Pedido**: Registre um novo pedido de venda
- **Adicionar Itens**: Adicione molas ao pedido individualmente ou múltiplas de uma vez
- **Registrar Venda sem Movimentar Estoque**: Opção para registrar vendas apenas para fins históricos
- **Atender Pedido**: Processe a saída de estoque para atender o pedido
- **Cancelar Pedido**: Cancele um pedido existente
- **Excluir Pedido**: Remova um pedido do sistema
- **Finalizar Pedido**: Marque um pedido como finalizado
- **Histórico**: Visualize o histórico de pedidos

#### Status de Pedidos

- **Pendente**: Pedido registrado mas não processado
- **Aprovado**: Pedido aprovado para atendimento
- **Em Produção**: Pedido em fase de produção
- **Finalizado**: Pedido concluído e entregue
- **Cancelado**: Pedido cancelado

#### Adição Múltipla de Itens

A funcionalidade de adição múltipla permite adicionar várias molas ao mesmo pedido de uma só vez:

1. Selecione a mola no dropdown
2. Informe a quantidade
3. Marque ou desmarque a opção "Movimentar" conforme necessário
4. Clique em "Adicionar Mola" para adicionar mais linhas
5. Pressione Enter para navegar entre campos e adicionar novas linhas automaticamente
6. Clique em "Salvar" para adicionar todos os itens ao pedido

### Ordens de Fabricação

O módulo de ordens de fabricação (anteriormente planejamento de produção) permite planejar e controlar a produção de molas.

#### Funcionalidades

- **Criar Ordem**: Crie uma nova ordem de fabricação
- **Selecionar Material**: Escolha entre todos os materiais do mesmo material padrão da mola
- **Calcular Necessidade de Material**: Veja quanto material será necessário
- **Iniciar Produção**: Marque uma ordem como em andamento
- **Finalizar Produção**: Registre a conclusão da produção e entrada no estoque
- **Cancelar Ordem**: Cancele uma ordem existente

#### Campos Importantes

- **Número da Ordem**: Identificador único da ordem
- **Mola**: Mola a ser fabricada
- **Material**: Material a ser utilizado (escolha entre todos do mesmo material padrão)
- **Quantidade**: Quantidade a ser produzida
- **Data Início**: Data de início da produção (preenchida automaticamente)
- **Status**: Estado atual da ordem (Pendente, Em Andamento, Finalizada, Cancelada)

#### Seleção de Material

Ao criar uma nova ordem de fabricação:

1. Selecione a mola desejada
2. O sistema mostrará todos os materiais do mesmo material padrão da mola
3. Selecione o material específico que será utilizado na produção
4. Isso permite flexibilidade para usar diferentes variações do mesmo material

### Previsão de Demanda

O módulo de previsão de demanda utiliza algoritmos estatísticos para prever a demanda futura de molas.

#### Funcionalidades

- **Gerar Previsão**: Crie previsões de demanda para molas específicas
- **Visualizar Previsões**: Veja previsões existentes com gráficos
- **Comparar com Histórico**: Compare previsões com dados históricos
- **Exportar Relatórios**: Exporte previsões em formato PDF ou CSV

#### Métodos de Previsão

- **Média Móvel**: Baseado na média das vendas recentes
- **Suavização Exponencial**: Dá mais peso a dados recentes
- **Regressão Linear**: Identifica tendências lineares nos dados

### Análise de Obsolescência

O módulo de análise de obsolescência identifica itens com baixa rotatividade ou sem movimentação.

#### Funcionalidades

- **Gerar Análise**: Crie uma análise de obsolescência do estoque
- **Classificar Itens**: Veja itens classificados por rotatividade
- **Recomendações**: Receba sugestões para itens obsoletos
- **Exportar Relatórios**: Exporte análises em formato PDF ou CSV

#### Classificações

- **Alta Rotatividade (A)**: Itens com movimentação frequente
- **Média Rotatividade (B)**: Itens com movimentação regular
- **Baixa Rotatividade (C)**: Itens com movimentação ocasional
- **Sem Movimentação (D)**: Itens sem movimentação recente
- **Obsoleto (O)**: Itens sem movimentação por período prolongado

### Relatórios

O módulo de relatórios permite gerar e exportar diversos relatórios do sistema.

#### Tipos de Relatórios

- **Estoque Atual**: Situação atual do estoque de molas e materiais
- **Movimentações**: Histórico de movimentações por período
- **Vendas**: Relatório de vendas por cliente, mola ou período
- **Molas Mais Vendidas**: Ranking das molas mais vendidas
- **Alertas de Estoque**: Itens abaixo do estoque mínimo
- **Previsão de Demanda**: Relatório de previsões geradas
- **Análise de Obsolescência**: Relatório de itens obsoletos

#### Formatos de Exportação

- **PDF**: Relatórios formatados para impressão
- **CSV**: Dados em formato tabular para análise em Excel
- **Tela**: Visualização direta no sistema

## Scripts e Ferramentas

### Scripts de Inicialização

- **iniciar_servidor_seguro.bat**: Inicia o servidor em modo de desenvolvimento (recomendado para uso diário)
- **iniciar_servidor_producao.bat**: Inicia o servidor em modo de produção (mais seguro, mas com menos recursos de depuração)
- **iniciar_servidor.bat**: Script original de inicialização (mantido por compatibilidade)

### Scripts de Segurança

- **gerar_secret_key.py**: Gera uma nova chave secreta para o sistema
- **executar_gerar_secret_key.bat**: Wrapper para facilitar a execução do script anterior
- **verificar_seguranca.py**: Verifica as configurações de segurança do sistema
- **executar_verificacao_seguranca.bat**: Wrapper para facilitar a execução do script anterior

### Scripts de Manutenção

- **criar_diretorios.py**: Cria os diretórios necessários para o sistema
- **diagnostico_sistema.py**: Realiza um diagnóstico completo do sistema
- **verificar_ambiente.py**: Verifica o ambiente de execução do sistema

### Arquivos de Configuração

- **.env**: Contém configurações sensíveis como chaves e senhas
- **.env.example**: Modelo para o arquivo .env
- **requirements.txt**: Lista de dependências do sistema

## Atalhos de Teclado

O sistema possui diversos atalhos de teclado para agilizar a operação. Consulte o documento `docs\atalhos_teclado.md` para uma lista completa.

### Atalhos Globais

- `Alt + N`: Criar novo item
- `Alt + S`: Salvar formulário
- `Alt + B`: Voltar para a página anterior
- `Enter`: Navegar entre campos de formulário

## Solução de Problemas

### Servidor Não Inicia

- Verifique se não há outro servidor já rodando
- Verifique se a porta 8000 não está sendo usada
- Tente reiniciar o computador

### Erro de Conexão

- Verifique se o servidor está rodando
- Tente acessar usando `localhost:8000` em vez de `127.0.0.1:8000`
- Verifique se não há firewall bloqueando a conexão

### Erro de Banco de Dados

- Verifique se o arquivo `db.sqlite3` existe e não está corrompido
- Tente fazer backup do banco e restaurar uma versão anterior

### Erros de Dependências

- Execute `pip install -r requirements.txt` para instalar todas as dependências
- Verifique se a versão do Python é compatível (3.8+)

### Contato para Suporte

Para suporte adicional, entre em contato com o desenvolvedor do sistema.

---

*Manual atualizado em: Junho de 2024*
*Versão do Sistema: 2.2*
