#!/usr/bin/env python
import os
import sys
import django

# Configurar Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'controle_estoque.settings')
django.setup()

from django.test import Client
from django.urls import reverse

def testar_relatorio():
    print("=== Testando Relatório de Estoque Sem Revenir ===")
    
    client = Client()
    
    # Testar GET
    try:
        url = reverse('relatorio-estoque-sem-revenir')
        print(f"URL: {url}")
        
        response = client.get(url)
        print(f"Status GET: {response.status_code}")
        
        if response.status_code != 200:
            print(f"Erro no GET: {response.content}")
        else:
            print("GET funcionou corretamente")
            
    except Exception as e:
        print(f"Erro no GET: {e}")
    
    # Testar POST
    try:
        response = client.post(url, {
            'cliente': '',
            'codigo': '',
            'formato': 'tela'
        })
        print(f"Status POST: {response.status_code}")
        
        if response.status_code != 200:
            print(f"Erro no POST: {response.content}")
        else:
            print("POST funcionou corretamente")
            
    except Exception as e:
        print(f"Erro no POST: {e}")

if __name__ == '__main__':
    testar_relatorio()
