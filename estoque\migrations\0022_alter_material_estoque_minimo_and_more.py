# Generated by Django 5.2 on 2025-05-19 14:18

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('estoque', '0021_planejamentoproducao_material'),
    ]

    operations = [
        migrations.AlterField(
            model_name='material',
            name='estoque_minimo',
            field=models.DecimalField(decimal_places=2, default=0, help_text='Quantidade mínima de estoque em kg', max_digits=10),
        ),
        migrations.AlterField(
            model_name='mola',
            name='estoque_minimo',
            field=models.IntegerField(default=0, help_text='Quantidade mínima de estoque'),
        ),
        migrations.AlterField(
            model_name='mola',
            name='peso_unitario',
            field=models.DecimalField(blank=True, decimal_places=4, help_text='Peso unitário da mola em gramas (medido manualmente)', max_digits=10, null=True),
        ),
    ]
