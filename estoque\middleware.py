"""
Middleware para lidar com problemas de timezone no Django.
"""

from django.utils import timezone


class TimezoneMiddleware:
    """
    Middleware para garantir que todos os datetimes sejam timezone-aware.
    
    Este middleware ajuda a evitar o aviso:
    "DateTimeField received a naive datetime while time zone support is active."
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        
    def __call__(self, request):
        # Ativa o timezone padrão para a thread atual
        timezone.activate(timezone.get_default_timezone())
        
        # Processa a requisição
        response = self.get_response(request)
        
        # Desativa o timezone para a thread atual
        timezone.deactivate()
        
        return response
