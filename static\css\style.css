/**
 * Estilos padronizados para o sistema de controle de estoque
 * Tema: Roxo/Lil<PERSON> (conforme preferência do usuário)
 */

:root {
  /* Cores principais */
  --primary-color: #6a1b9a;       /* Roxo escuro */
  --primary-light: #9c4dcc;       /* Roxo claro */
  --primary-dark: #38006b;        /* Roxo muito escuro */
  --secondary-color: #e1bee7;     /* <PERSON><PERSON> claro */
  --accent-color: #8e24aa;        /* Roxo médio */

  /* Cores de status */
  --success-color: #4caf50;       /* Verde */
  --warning-color: #ff9800;       /* <PERSON><PERSON> */
  --danger-color: #f44336;        /* Vermelho */
  --info-color: #2196f3;          /* Azul */

  /* Cores de fundo */
  --bg-dark: #212121;             /* Fundo escuro */
  --bg-medium: #303030;           /* Fundo médio */
  --bg-light: #424242;            /* Fundo claro */

  /* Cores de texto */
  --text-light: #f5f5f5;          /* Texto claro */
  --text-medium: #bdbdbd;         /* Texto médio */
  --text-dark: #757575;           /* Texto escuro */

  /* Espaçamentos */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;

  /* Bordas */
  --border-radius: 4px;
  --border-radius-lg: 8px;

  /* Sombras */
  --shadow-sm: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
  --shadow-md: 0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23);
  --shadow-lg: 0 10px 20px rgba(0,0,0,0.19), 0 6px 6px rgba(0,0,0,0.23);
}

/* Estilos gerais */
body {
  background-color: var(--bg-dark);
  color: var(--text-light);
  font-family: 'Roboto', 'Segoe UI', Arial, sans-serif;
  line-height: 1.6;
  margin: 0;
  padding: 0;
}

.container {
  padding: var(--spacing-md);
}

/* Cabeçalhos */
h1, h2, h3, h4, h5, h6 {
  color: var(--text-light);
  margin-top: var(--spacing-lg);
  margin-bottom: var(--spacing-md);
}

h1 {
  font-size: 2rem;
  border-bottom: 2px solid var(--primary-color);
  padding-bottom: var(--spacing-sm);
}

h2 {
  font-size: 1.75rem;
}

h3 {
  font-size: 1.5rem;
}

/* Links */
a {
  color: var(--primary-light);
  text-decoration: none;
  transition: color 0.3s ease;
}

a:hover {
  color: var(--secondary-color);
  text-decoration: underline;
}

/* Botões */
.btn {
  display: inline-block;
  font-weight: 400;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  user-select: none;
  border: 1px solid transparent;
  padding: 0.375rem 0.75rem;
  font-size: 1rem;
  line-height: 1.5;
  border-radius: var(--border-radius);
  transition: all 0.15s ease-in-out;
  cursor: pointer;
}

.btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
}

.btn-secondary {
  background-color: var(--bg-light);
  border-color: var(--bg-light);
  color: var(--text-light);
}

.btn-secondary:hover {
  background-color: var(--bg-medium);
  border-color: var(--bg-medium);
}

.btn-success {
  background-color: var(--success-color);
  border-color: var(--success-color);
  color: white;
}

.btn-warning {
  background-color: var(--warning-color);
  border-color: var(--warning-color);
  color: white;
}

.btn-danger {
  background-color: var(--danger-color);
  border-color: var(--danger-color);
  color: white;
}

.btn-info {
  background-color: var(--info-color);
  border-color: var(--info-color);
  color: white;
}

/* Tabelas */
.table {
  width: 100%;
  margin-bottom: var(--spacing-lg);
  color: var(--text-light);
  border-collapse: collapse;
}

.table th,
.table td {
  padding: var(--spacing-sm) var(--spacing-md);
  vertical-align: top;
  border-top: 1px solid var(--bg-light);
}

.table thead th {
  vertical-align: bottom;
  border-bottom: 2px solid var(--primary-color);
  background-color: var(--bg-medium);
}

.table tbody tr:nth-of-type(odd) {
  background-color: rgba(255, 255, 255, 0.05);
}

.table tbody tr:hover {
  background-color: rgba(255, 255, 255, 0.075);
}

/* Cards */
.card {
  position: relative;
  display: flex;
  flex-direction: column;
  min-width: 0;
  word-wrap: break-word;
  background-color: var(--bg-medium);
  background-clip: border-box;
  border: 1px solid var(--bg-light);
  border-radius: var(--border-radius);
  margin-bottom: var(--spacing-md);
  box-shadow: var(--shadow-sm);
  transition: box-shadow 0.3s ease;
}

.card:hover {
  box-shadow: var(--shadow-md);
}

.card-header {
  padding: var(--spacing-md);
  margin-bottom: 0;
  background-color: var(--bg-light);
  border-bottom: 1px solid var(--bg-light);
}

.card-body {
  flex: 1 1 auto;
  padding: var(--spacing-md);
}

.card-title {
  margin-bottom: var(--spacing-md);
  font-size: 1.25rem;
}

.card-text:last-child {
  margin-bottom: 0;
}

/* Alertas */
.alert {
  position: relative;
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-md);
  border: 1px solid transparent;
  border-radius: var(--border-radius);
}

.alert-success {
  display: none !important; /* Ocultar completamente os alertas de sucesso */
}

.alert-warning {
  color: #fff8e1;
  background-color: #f57f17;
  border-color: #ff6f00;
}

.alert-danger {
  color: #ffebee;
  background-color: #c62828;
  border-color: #b71c1c;
}

.alert-info {
  color: #e3f2fd;
  background-color: #1565c0;
  border-color: #0d47a1;
}

/* Formulários */
.form-group {
  margin-bottom: var(--spacing-md);
}

.form-control {
  display: block;
  width: 100%;
  padding: 0.375rem 0.75rem;
  font-size: 1rem;
  line-height: 1.5;
  color: var(--text-light);
  background-color: var(--bg-light);
  background-clip: padding-box;
  border: 1px solid var(--bg-light);
  border-radius: var(--border-radius);
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
  color: var(--text-light);
  background-color: var(--bg-light);
  border-color: var(--primary-light);
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(106, 27, 154, 0.25);
}

/* Corrigir caixas de seleção para não mostrar linhas de seleção */
select.form-control,
select.form-select {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%23f5f5f5' viewBox='0 0 16 16'%3E%3Cpath d='M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 16px 12px;
  padding-right: 2.5rem;
}

/* Corrigir opções dentro dos selects para exibir apenas uma linha */
select.form-control option,
select.form-select option {
  padding: 8px 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  background-color: var(--bg-medium);
  color: var(--text-light);
  border: none;
  line-height: 1.2;
}

/* Tooltips */
.tooltip {
  position: relative;
  display: inline-block;
}

.tooltip .tooltip-text {
  visibility: hidden;
  width: 120px;
  background-color: var(--bg-dark);
  color: var(--text-light);
  text-align: center;
  border-radius: var(--border-radius);
  padding: 5px;
  position: absolute;
  z-index: 1;
  bottom: 125%;
  left: 50%;
  margin-left: -60px;
  opacity: 0;
  transition: opacity 0.3s;
}

.tooltip:hover .tooltip-text {
  visibility: visible;
  opacity: 1;
}

/* Status de pedidos */
.status-pendente {
  color: var(--warning-color);
}

.status-em-producao {
  color: var(--info-color);
}

.status-aprovado {
  color: var(--success-color);
}

.status-cancelado {
  color: var(--danger-color);
}

.status-finalizado {
  color: var(--text-medium);
}

/* Responsividade */
@media (max-width: 768px) {
  .container {
    padding: var(--spacing-sm);
  }

  .table {
    display: block;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  .card {
    margin-bottom: var(--spacing-md);
  }
}
