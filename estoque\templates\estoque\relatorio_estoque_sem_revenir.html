{% extends 'estoque/base.html' %}

{% block title %}Relatório de Estoque Sem Revenir - Controle de Estoque{% endblock %}

{% block content %}
<div class="container-fluid">
    <h1 class="mt-4 mb-4">Relatório de Estoque Sem Revenir</h1>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Filtros</h6>
        </div>
        <div class="card-body">
            <form method="post" class="row g-3">
                {% csrf_token %}

                <div class="col-md-4">
                    <label for="cliente" class="form-label">Cliente</label>
                    <input type="text" name="cliente" id="cliente" class="form-control" value="{{ filtro_cliente|default:'' }}">
                </div>

                <div class="col-md-4">
                    <label for="codigo" class="form-label"><PERSON><PERSON><PERSON> da Mola</label>
                    <input type="text" name="codigo" id="codigo" class="form-control" value="{{ filtro_codigo|default:'' }}">
                </div>

                <div class="col-12 mt-3">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> Filtrar
                    </button>

                    <div class="float-end">
                        <button type="submit" name="formato" value="pdf" class="btn btn-danger">
                            <i class="fas fa-file-pdf"></i> Exportar PDF
                        </button>
                        <button type="submit" name="formato" value="csv" class="btn btn-success">
                            <i class="fas fa-file-csv"></i> Exportar CSV
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    {% if estoques %}
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">
                    Relatório de Estoque Sem Revenir
                    {% if filtro_cliente %} - Cliente: {{ filtro_cliente }}{% endif %}
                    {% if filtro_codigo %} - Código: {{ filtro_codigo }}{% endif %}
                </h6>
                <button onclick="window.print()" class="btn btn-sm btn-secondary">
                    <i class="fas fa-print"></i> Imprimir
                </button>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th style="width: 20%; text-align: left;">Código</th>
                                <th style="width: 20%; text-align: left;">Cliente</th>
                                <th style="width: 15%; text-align: left;">Material</th>
                                <th style="width: 10%; text-align: center;">Diâmetro</th>
                                <th style="width: 10%; text-align: center;">Est. Atual</th>
                                <th style="width: 25%; text-align: left;">Anotações</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for estoque in estoques %}
                                <tr>
                                    <td style="text-align: left;">{% if estoque.mola.tipo_apelido %}{{ estoque.mola.codigo }} - {{ estoque.mola.tipo_apelido }}{% else %}{{ estoque.mola.codigo }}{% endif %}</td>
                                    <td style="text-align: left;">{{ estoque.mola.cliente }}</td>
                                    <td style="text-align: left;">
                                        {% if estoque.mola.material_padrao %}
                                            {% load estoque_filters %}
                                            {{ estoque.mola.material_padrao.nome|abreviar_material }}
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                    <td style="text-align: center;">{% if estoque.mola.diametro %}{{ estoque.mola.diametro }}{% elif estoque.mola.material_padrao and estoque.mola.material_padrao.diametro %}{{ estoque.mola.material_padrao.diametro }}{% elif estoque.mola.material and estoque.mola.material.diametro %}{{ estoque.mola.material.diametro }}{% else %}-{% endif %}</td>
                                    <td style="text-align: center;">{{ estoque.quantidade }}</td>
                                    <td style="text-align: left;">{{ estoque.observacoes|default:"" }}</td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <div class="mt-3">
                    <p><strong>Total de Molas:</strong> {{ total_molas }}</p>
                    <p><strong>Quantidade Total em Estoque:</strong> {{ total_quantidade }}</p>
                </div>
            </div>
        </div>
    {% elif request.method == 'POST' %}
        <div class="alert alert-info alert-dismissible fade show">
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Fechar"></button>
            Nenhuma mola encontrada com os filtros selecionados.
        </div>
    {% endif %}
</div>

<style>
    @media print {
        .navbar, .sidebar, .footer, .card-header button, form, .btn {
            display: none !important;
        }

        .card {
            border: none !important;
            box-shadow: none !important;
        }

        .card-header {
            background-color: white !important;
            color: black !important;
            border-bottom: 1px solid #ddd !important;
        }

        body {
            background-color: white !important;
            color: black !important;
        }

        .table {
            color: black !important;
        }
    }
</style>
{% endblock %}
