{% extends 'estoque/base.html' %}
{% load static %}

{% block title %}Movimentar Estoque Sem Revenir{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-exchange-alt"></i> Movimentar Estoque Sem Revenir
                    </h3>
                    <div class="card-tools">
                        <a href="{% url 'estoque-sem-revenir-list' %}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> Voltar
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    <!-- Informações da Mola -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="alert alert-info">
                                <h5><i class="fas fa-info-circle"></i> Informações da Mola</h5>
                                <div class="row">
                                    <div class="col-md-3">
                                        <strong>Código:</strong> {{ estoque.mola.codigo }}
                                    </div>
                                    <div class="col-md-3">
                                        <strong>Cliente:</strong> {{ estoque.mola.cliente }}
                                    </div>
                                    <div class="col-md-3">
                                        <strong>Quantidade Disponível:</strong> 
                                        <span class="badge bg-primary">{{ estoque.quantidade }}</span>
                                    </div>
                                    <div class="col-md-3">
                                        <strong>Última Movimentação:</strong> 
                                        {{ estoque.data_ultima_movimentacao|date:"d/m/Y H:i" }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="tipo" class="form-label">Tipo de Movimentação</label>
                                    <select name="tipo" id="tipo" class="form-select" required>
                                        <option value="">Selecione o tipo...</option>
                                        <option value="S">Saída (Remoção do estoque)</option>
                                        <option value="T">Transferência para estoque normal</option>
                                    </select>
                                    <div class="form-text">
                                        Escolha o tipo de movimentação desejada.
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="quantidade" class="form-label">Quantidade</label>
                                    <input type="number" 
                                           name="quantidade" 
                                           id="quantidade" 
                                           class="form-control" 
                                           min="1" 
                                           max="{{ estoque.quantidade }}"
                                           required>
                                    <div class="form-text">
                                        Máximo: {{ estoque.quantidade }} unidades
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="form-group mb-3">
                                    <label for="observacao" class="form-label">Observação</label>
                                    <textarea name="observacao" 
                                              id="observacao" 
                                              class="form-control" 
                                              rows="3" 
                                              placeholder="Motivo ou observações sobre esta movimentação (opcional)"></textarea>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                    <a href="{% url 'estoque-sem-revenir-list' %}" class="btn btn-secondary me-md-2">
                                        <i class="fas fa-times"></i> Cancelar
                                    </a>
                                    <button type="submit" class="btn btn-warning">
                                        <i class="fas fa-exchange-alt"></i> Executar Movimentação
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Focar no campo de tipo ao carregar a página
    document.getElementById('tipo').focus();
    
    // Quando um tipo for selecionado, focar no campo quantidade
    document.getElementById('tipo').addEventListener('change', function() {
        if (this.value) {
            document.getElementById('quantidade').focus();
        }
    });
    
    // Permitir navegação com Enter
    document.getElementById('quantidade').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            document.getElementById('observacao').focus();
        }
    });
    
    // Validação da quantidade
    document.getElementById('quantidade').addEventListener('input', function() {
        const max = parseInt(this.getAttribute('max'));
        const value = parseInt(this.value);
        
        if (value > max) {
            this.setCustomValidity(`A quantidade não pode ser maior que ${max}`);
        } else {
            this.setCustomValidity('');
        }
    });
});
</script>
{% endblock %}
