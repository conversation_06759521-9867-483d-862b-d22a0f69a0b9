#!/usr/bin/env python
"""
Script simples para testar se as correções do matplotlib funcionaram.
"""
import os
import sys

def testar_matplotlib():
    """Testa se o matplotlib está configurado corretamente."""
    print("🔧 Testando configuração do matplotlib...")
    
    try:
        # Configurar backend antes de importar
        os.environ['MPLBACKEND'] = 'Agg'
        
        import matplotlib
        matplotlib.use('Agg', force=True)
        
        import matplotlib.pyplot as plt
        plt.ioff()
        
        # Configurações adicionais
        matplotlib.rcParams['backend'] = 'Agg'
        matplotlib.rcParams['interactive'] = False
        
        print(f"✅ Backend configurado: {matplotlib.get_backend()}")
        print(f"✅ Modo interativo: {matplotlib.is_interactive()}")
        
        # Testar criação de gráfico simples
        fig, ax = plt.subplots(figsize=(4, 3))
        ax.plot([1, 2, 3], [1, 4, 2])
        ax.set_title('Teste')
        
        # Salvar em buffer
        import io
        buffer = io.BytesIO()
        plt.savefig(buffer, format='png', dpi=150)
        buffer.seek(0)
        
        plt.close(fig)
        plt.clf()
        
        print("✅ Gráfico criado e salvo com sucesso!")
        print(f"✅ Tamanho do buffer: {len(buffer.getvalue())} bytes")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro na configuração do matplotlib: {e}")
        import traceback
        traceback.print_exc()
        return False

def testar_django_imports():
    """Testa se os imports do Django estão funcionando."""
    print("\n🔧 Testando imports do Django...")
    
    try:
        # Configurar Django
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'controle_estoque.settings')
        
        import django
        django.setup()
        
        # Testar imports críticos
        from estoque.models import Mola, Material, MovimentacaoEstoque
        print("✅ Models importados com sucesso!")
        
        from estoque.report_utils import create_chart, create_line_chart
        print("✅ Report utils importados com sucesso!")
        
        # Testar criação de gráfico usando report_utils
        import numpy as np
        data = [10, 20, 15, 25, 30]
        labels = ['A', 'B', 'C', 'D', 'E']
        
        chart = create_chart(data, labels, "Teste de Gráfico")
        print("✅ Gráfico criado usando report_utils!")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro nos imports do Django: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Função principal do teste."""
    print("=" * 60)
    print("🧪 TESTE SIMPLES DE CORREÇÕES DO MATPLOTLIB")
    print("=" * 60)
    
    # Teste 1: Matplotlib básico
    resultado1 = testar_matplotlib()
    
    # Teste 2: Django + Matplotlib
    resultado2 = testar_django_imports()
    
    # Resumo
    print("\n" + "=" * 60)
    print("📊 RESUMO DOS TESTES")
    print("=" * 60)
    
    print(f"Matplotlib básico: {'✅ PASSOU' if resultado1 else '❌ FALHOU'}")
    print(f"Django + Matplotlib: {'✅ PASSOU' if resultado2 else '❌ FALHOU'}")
    
    if resultado1 and resultado2:
        print("\n🎉 TODOS OS TESTES PASSARAM!")
        print("✅ O erro 'main thread is not in main loop' foi corrigido!")
        print("✅ O sistema está pronto para uso!")
    else:
        print("\n⚠️ ALGUNS TESTES FALHARAM.")
        print("❌ Verifique os erros acima.")
    
    print("=" * 60)

if __name__ == '__main__':
    main()
