{% extends 'estoque/base.html' %}
{% load static %}

{% block title %}Comparação entre Períodos - <PERSON><PERSON> Mais Vendidas{% endblock %}

{% block content %}
<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">Comparação entre Períodos - <PERSON><PERSON> Mais Vendidas</h1>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Filtros</h6>
        </div>
        <div class="card-body">
            <form method="post" class="row g-3">
                {% csrf_token %}

                <div class="col-md-3">
                    <label for="{{ form.periodo.id_for_label }}" class="form-label">Período</label>
                    {{ form.periodo.errors }}
                    <select name="{{ form.periodo.name }}" id="{{ form.periodo.id_for_label }}" class="form-select">
                        {% for value, text in form.fields.periodo.choices %}
                            <option value="{{ value }}" {% if form.periodo.value == value %}selected{% endif %}>{{ text }}</option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Campos para comparação entre períodos -->
                <div class="col-md-12 mt-3 comparacao-periodos" style="display: {% if form.periodo.value == 'comparacao' %}block{% else %}none{% endif %};">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">Configuração dos Períodos para Comparação</h6>
                        </div>
                        <div class="card-body">
                            <!-- Seletores intuitivos de mês/ano -->
                            <div class="row mb-3">
                                <div class="col-md-12">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="usarSeletoresMesAno" checked>
                                        <label class="form-check-label" for="usarSeletoresMesAno">
                                            Usar seletores de mês/ano (recomendado)
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <!-- Seletores de mês/ano -->
                            <div class="row seletores-mes-ano">
                                <div class="col-md-6">
                                    <h6 class="text-primary">Período 1</h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <label for="{{ form.mes_comparacao1.id_for_label }}" class="form-label">{{ form.mes_comparacao1.label }}</label>
                                            {{ form.mes_comparacao1.errors }}
                                            {{ form.mes_comparacao1 }}
                                        </div>
                                        <div class="col-md-6">
                                            <label for="{{ form.ano_comparacao1.id_for_label }}" class="form-label">{{ form.ano_comparacao1.label }}</label>
                                            {{ form.ano_comparacao1.errors }}
                                            {{ form.ano_comparacao1 }}
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-primary">Período 2</h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <label for="{{ form.mes_comparacao2.id_for_label }}" class="form-label">{{ form.mes_comparacao2.label }}</label>
                                            {{ form.mes_comparacao2.errors }}
                                            {{ form.mes_comparacao2 }}
                                        </div>
                                        <div class="col-md-6">
                                            <label for="{{ form.ano_comparacao2.id_for_label }}" class="form-label">{{ form.ano_comparacao2.label }}</label>
                                            {{ form.ano_comparacao2.errors }}
                                            {{ form.ano_comparacao2 }}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Campos de data manual (ocultos por padrão) -->
                            <div class="row campos-data-manual" style="display: none;">
                                <div class="col-md-6">
                                    <h6 class="text-primary">Período 1</h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <label for="{{ form.data_inicial_comparacao1.id_for_label }}" class="form-label">{{ form.data_inicial_comparacao1.label }}</label>
                                            {{ form.data_inicial_comparacao1.errors }}
                                            {{ form.data_inicial_comparacao1 }}
                                        </div>
                                        <div class="col-md-6">
                                            <label for="{{ form.data_final_comparacao1.id_for_label }}" class="form-label">{{ form.data_final_comparacao1.label }}</label>
                                            {{ form.data_final_comparacao1.errors }}
                                            {{ form.data_final_comparacao1 }}
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-primary">Período 2</h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <label for="{{ form.data_inicial_comparacao2.id_for_label }}" class="form-label">{{ form.data_inicial_comparacao2.label }}</label>
                                            {{ form.data_inicial_comparacao2.errors }}
                                            {{ form.data_inicial_comparacao2 }}
                                        </div>
                                        <div class="col-md-6">
                                            <label for="{{ form.data_final_comparacao2.id_for_label }}" class="form-label">{{ form.data_final_comparacao2.label }}</label>
                                            {{ form.data_final_comparacao2.errors }}
                                            {{ form.data_final_comparacao2 }}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Campo de subtítulo personalizado -->
                            <div class="row mt-3">
                                <div class="col-md-12">
                                    <label for="{{ form.subtitulo_personalizado_comparacao.id_for_label }}" class="form-label">{{ form.subtitulo_personalizado_comparacao.label }}</label>
                                    {{ form.subtitulo_personalizado_comparacao.errors }}
                                    {{ form.subtitulo_personalizado_comparacao }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-3">
                    <label for="{{ form.cliente.id_for_label }}" class="form-label">{{ form.cliente.label }}</label>
                    {{ form.cliente.errors }}
                    {{ form.cliente }}
                </div>

                <div class="col-md-3">
                    <label for="{{ form.limite.id_for_label }}" class="form-label">{{ form.limite.label }}</label>
                    {{ form.limite.errors }}
                    {{ form.limite }}
                </div>

                <div class="col-12">
                    <button type="submit" name="formato" value="html" class="btn btn-primary">
                        <i class="fas fa-search"></i> Gerar Relatório
                    </button>
                    {% if resultado_comparacao %}
                        <button type="submit" name="formato" value="pdf" class="btn btn-danger">
                            <i class="fas fa-file-pdf"></i> Exportar PDF
                        </button>
                        <button type="submit" name="formato" value="csv" class="btn btn-success">
                            <i class="fas fa-file-csv"></i> Exportar CSV
                        </button>
                    {% endif %}
                </div>
            </form>
        </div>
    </div>

    {% if resultado_comparacao %}
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">
                    Comparação entre Períodos
                </h6>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="card border-left-primary">
                            <div class="card-body">
                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Período 1</div>
                                <div class="h6 mb-0 font-weight-bold text-gray-800">{{ periodo1_texto }}</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card border-left-info">
                            <div class="card-body">
                                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Período 2</div>
                                <div class="h6 mb-0 font-weight-bold text-gray-800">{{ periodo2_texto }}</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>Posição</th>
                                <th>Código</th>
                                <th>Cliente</th>
                                <th>Período 1</th>
                                <th>Período 2</th>
                                <th>Variação %</th>
                                <th>Diferença</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in resultado_comparacao %}
                                <tr>
                                    <td>{{ forloop.counter }}</td>
                                    <td>{{ item.mola_codigo }}</td>
                                    <td>{{ item.mola_cliente }}</td>
                                    <td>{{ item.vendas_periodo1 }}</td>
                                    <td>{{ item.vendas_periodo2 }}</td>
                                    <td>
                                        {% if item.variacao_percentual > 0 %}
                                            <span class="text-success">+{{ item.variacao_percentual }}%</span>
                                        {% elif item.variacao_percentual < 0 %}
                                            <span class="text-danger">{{ item.variacao_percentual }}%</span>
                                        {% else %}
                                            <span class="text-muted">0%</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if item.diferenca_absoluta > 0 %}
                                            <span class="text-success">+{{ item.diferenca_absoluta }}</span>
                                        {% elif item.diferenca_absoluta < 0 %}
                                            <span class="text-danger">{{ item.diferenca_absoluta }}</span>
                                        {% else %}
                                            <span class="text-muted">0</span>
                                        {% endif %}
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Resumo -->
                <div class="row mt-4">
                    <div class="col-md-12">
                        <h6 class="font-weight-bold text-primary">Resumo da Comparação</h6>
                        <div class="row">
                            <div class="col-md-3">
                                <div class="card border-left-primary">
                                    <div class="card-body">
                                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Período 1</div>
                                        <div class="h6 mb-0 font-weight-bold text-gray-800">
                                            {% with total_p1=resultado_comparacao|length %}
                                                {% for item in resultado_comparacao %}
                                                    {% if forloop.first %}
                                                        {% with total=0 %}
                                                            {% for i in resultado_comparacao %}
                                                                {% with total=total|add:i.vendas_periodo1 %}
                                                                    {% if forloop.last %}{{ total }}{% endif %}
                                                                {% endwith %}
                                                            {% endfor %}
                                                        {% endwith %}
                                                    {% endif %}
                                                {% endfor %}
                                            {% endwith %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card border-left-info">
                                    <div class="card-body">
                                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Total Período 2</div>
                                        <div class="h6 mb-0 font-weight-bold text-gray-800">
                                            {% for item in resultado_comparacao %}
                                                {% if forloop.first %}
                                                    {% with total=0 %}
                                                        {% for i in resultado_comparacao %}
                                                            {% with total=total|add:i.vendas_periodo2 %}
                                                                {% if forloop.last %}{{ total }}{% endif %}
                                                            {% endwith %}
                                                        {% endfor %}
                                                    {% endwith %}
                                                {% endif %}
                                            {% endfor %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    {% elif request.method == 'POST' %}
        <div class="alert alert-info alert-dismissible fade show">
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Fechar"></button>
            Nenhuma mola encontrada com os filtros selecionados.
        </div>
    {% endif %}
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const periodoSelect = document.getElementById('{{ form.periodo.id_for_label }}');
    const comparacaoDiv = document.querySelector('.comparacao-periodos');

    function toggleComparacao() {
        if (periodoSelect.value === 'comparacao') {
            comparacaoDiv.style.display = 'block';
        } else {
            comparacaoDiv.style.display = 'none';
        }
    }

    periodoSelect.addEventListener('change', toggleComparacao);
    toggleComparacao(); // Executar na inicialização

    // Controle dos seletores de mês/ano vs campos de data manual
    const checkboxSeletores = document.getElementById('usarSeletoresMesAno');
    const seletoresMesAno = document.querySelector('.seletores-mes-ano');
    const camposDataManual = document.querySelector('.campos-data-manual');

    function toggleSeletores() {
        if (checkboxSeletores && checkboxSeletores.checked) {
            if (seletoresMesAno) seletoresMesAno.style.display = 'block';
            if (camposDataManual) camposDataManual.style.display = 'none';
        } else {
            if (seletoresMesAno) seletoresMesAno.style.display = 'none';
            if (camposDataManual) camposDataManual.style.display = 'block';
        }
    }

    if (checkboxSeletores) {
        checkboxSeletores.addEventListener('change', toggleSeletores);
        toggleSeletores(); // Executar na inicialização
    }
});
</script>
{% endblock %}
