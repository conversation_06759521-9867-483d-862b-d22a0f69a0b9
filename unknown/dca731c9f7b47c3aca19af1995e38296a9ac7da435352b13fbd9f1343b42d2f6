import numpy as np

from statsmodels.tools.tools import Bunch

llf = np.array([-243.77512585356])

nobs = np.array([202])

k = np.array([3])

k_exog = np.array([1])

sigma = np.array([.80556855709271])

chi2 = np.array([14938.241729056])

df_model = np.array([2])

k_ar = np.array([1])

k_ma = np.array([1])

params = np.array([
    .99034248845249,
    -.83659509233745,
    .80556855709271])

cov_params = np.array([
    .00009906057555,
    -.00026616895902,
    .00007867120825,
    -.00026616895902,
    .00137590666911,
    -.0001403880509,
    .00007867120825,
    -.0001403880509,
    .0002129852258]).reshape(3, 3)

xb = np.array([
    0,
    0,
    .10457526892424,
    .14047028124332,
    .10415132343769,
    .11891452968121,
    .09492295235395,
    .11409470438957,
    .1086763292551,
    .08389142900705,
    .08740901201963,
    .08212262392044,
    .07780049741268,
    .09159570932388,
    .07793674618006,
    .08996207267046,
    .08444581180811,
    .07675409317017,
    .09658851474524,
    .09001308679581,
    .10454939305782,
    .08898131549358,
    .08520055562258,
    .08665482699871,
    .09710033237934,
    .09660832583904,
    .11157563328743,
    .10410477221012,
    .12245775014162,
    .16395011544228,
    .16329728066921,
    .19811384379864,
    .1734282374382,
    .17583830654621,
    .19323042035103,
    .20777989923954,
    .23532645404339,
    .24299769103527,
    .28016451001167,
    .29588291049004,
    .30903342366219,
    .35078409314156,
    .37033796310425,
    .38669663667679,
    .41575729846954,
    .44006872177124,
    .42965853214264,
    .43632391095161,
    .44190016388893,
    .40044051408768,
    .41188025474548,
    .3907016813755,
    .37298321723938,
    .3581600189209,
    .3457590341568,
    .35075950622559,
    .37031736969948,
    .46355310082436,
    .46467992663383,
    .60399496555328,
    .68979620933533,
    .77695161104202,
    .8344908952713,
    .95950168371201,
    1.0025858879089,
    .94638174772263,
    .94548571109772,
    .92936158180237,
    .91587167978287,
    .81233781576157,
    .81797075271606,
    .8226832151413,
    .81125050783157,
    .87855970859528,
    .85799652338028,
    .84079349040985,
    .87252616882324,
    .9144481420517,
    1.0110183954239,
    1.0918086767197,
    1.1286484003067,
    1.2670909166336,
    1.4290360212326,
    1.533768415451,
    1.7136362791061,
    1.8794873952866,
    1.8337399959564,
    1.8569672107697,
    1.9378981590271,
    1.9133563041687,
    1.969698548317,
    1.939960360527,
    1.7767087221146,
    1.5786340236664,
    1.7050459384918,
    1.5186812877655,
    1.2397723197937,
    1.1755603551865,
    1.1372153759003,
    1.1051361560822,
    1.1244224309921,
    1.1251838207245,
    1.06432056427,
    1.0441527366638,
    .96578127145767,
    1.0078399181366,
    .95077663660049,
    .91841346025467,
    .98358678817749,
    .63836628198624,
    .65705251693726,
    .65730959177017,
    .73439955711365,
    .81426596641541,
    .85033398866653,
    .89588165283203,
    .90323758125305,
    .94014054536819,
    .98638904094696,
    1.040454864502,
    1.0703103542328,
    1.1875365972519,
    1.2087339162827,
    1.1495937108994,
    1.2846138477325,
    1.2899470329285,
    1.3251601457596,
    1.5544888973236,
    1.5003498792648,
    1.316685795784,
    1.2706536054611,
    1.2167699337006,
    1.1870667934418,
    1.1622149944305,
    1.1414264440536,
    1.1394081115723,
    1.1223464012146,
    1.0926969051361,
    1.0217674970627,
    1.0239287614822,
    .96423649787903,
    .94504725933075,
    .97511827945709,
    .96952658891678,
    .98022425174713,
    1.0199228525162,
    .97626084089279,
    .95510673522949,
    .98353403806686,
    1.0380674600601,
    1.0068138837814,
    1.0267919301987,
    1.0435055494308,
    .94986528158188,
    .87152636051178,
    .89823776483536,
    .82833498716354,
    .72372996807098,
    .75921636819839,
    .74277937412262,
    .74440395832062,
    .80726110935211,
    .79834908246994,
    .88314270973206,
    .92332923412323,
    1.0184471607208,
    1.12877368927,
    1.1288229227066,
    1.2057402133942,
    1.1317123174667,
    1.100532412529,
    .95145136117935,
    .81135284900665,
    .92477059364319,
    .88128125667572,
    .92177194356918,
    .98639768362045,
    .91746246814728,
    .84441828727722,
    .89093261957169,
    .96059763431549,
    .97275197505951,
    1.0751719474792,
    1.1608537435532,
    1.124911904335,
    1.2485905885696,
    1.1829364299774,
    1.6815021038055,
    1.4374854564667,
    1.4024653434753,
    1.4807903766632,
    1.1158236265182,
    1.1908674240112,
    1.3569641113281,
    1.3532432317734,
    1.4080929756165,
    1.6949023008347,
    1.6488753557205,
    2.0886788368225,
    1.4827802181244,
    .51556593179703,
    .5077338218689,
    .70120370388031])

y = np.array([
    np.nan,
    28.979999542236,
    29.25457572937,
    29.49047088623,
    29.474151611328,
    29.65891456604,
    29.64492225647,
    29.864093780518,
    29.948677062988,
    29.893890380859,
    30.00740814209,
    30.062122344971,
    30.11780166626,
    30.301595687866,
    30.29793548584,
    30.469961166382,
    30.524446487427,
    30.556753158569,
    30.786588668823,
    30.840013504028,
    31.044549942017,
    31.038982391357,
    31.105201721191,
    31.206655502319,
    31.377101898193,
    31.476608276367,
    31.691576004028,
    31.754104614258,
    32.002456665039,
    32.443950653076,
    32.613296508789,
    33.048110961914,
    33.073429107666,
    33.27583694458,
    33.593231201172,
    33.907779693604,
    34.33532333374,
    34.642997741699,
    35.180164337158,
    35.595882415771,
    36.009033203125,
    36.650783538818,
    37.170337677002,
    37.686695098877,
    38.315757751465,
    38.94006729126,
    39.329658508301,
    39.836326599121,
    40.341899871826,
    40.500438690186,
    41.011878967285,
    41.290702819824,
    41.572982788086,
    41.85816192627,
    42.14575958252,
    42.550758361816,
    43.070316314697,
    44.163555145264,
    44.664680480957,
    46.203994750977,
    47.489795684814,
    48.876949310303,
    50.134490966797,
    51.959503173828,
    53.302585601807,
    53.946380615234,
    54.945484161377,
    55.829364776611,
    56.715869903564,
    56.912334442139,
    57.817970275879,
    58.722682952881,
    59.511249542236,
    60.878559112549,
    61.657997131348,
    62.44079208374,
    63.572528839111,
    64.814453125,
    66.511016845703,
    68.19181060791,
    69.628646850586,
    71.867088317871,
    74.429039001465,
    76.733764648438,
    79.713638305664,
    82.779487609863,
    84.433738708496,
    86.55696105957,
    89.137893676758,
    91.01335144043,
    93.469696044922,
    95.339958190918,
    96.176712036133,
    96.578636169434,
    99.205047607422,
    99.618682861328,
    99.139770507813,
    99.975563049316,
    100.9372177124,
    101.9051361084,
    103.22441864014,
    104.42518615723,
    105.16432189941,
    106.14414978027,
    106.66577911377,
    108.00784301758,
    108.65077209473,
    109.41841125488,
    110.88359069824,
    109.33836364746,
    110.15705108643,
    110.85730743408,
    112.13439941406,
    113.51425933838,
    114.65033721924,
    115.89588165283,
    116.90323638916,
    118.14013671875,
    119.48638916016,
    120.94045257568,
    122.27030944824,
    124.28753662109,
    125.70873260498,
    126.54959869385,
    128.78460693359,
    130.18994140625,
    131.82516479492,
    134.95448303223,
    136.20034790039,
    136.41668701172,
    137.47065734863,
    138.41676330566,
    139.48707580566,
    140.56221008301,
    141.64143371582,
    142.83940124512,
    143.92234802246,
    144.89270019531,
    145.52177429199,
    146.62393188477,
    147.2642364502,
    148.14505004883,
    149.37510681152,
    150.36952209473,
    151.48022460938,
    152.81993103027,
    153.57626342773,
    154.45510864258,
    155.68353271484,
    157.13807678223,
    158.00682067871,
    159.22679138184,
    160.4434967041,
    160.84985351563,
    161.27151489258,
    162.39823913574,
    162.82833862305,
    162.92372131348,
    163.95921325684,
    164.64277648926,
    165.44439697266,
    166.70726013184,
    167.49835205078,
    168.98315429688,
    170.22332763672,
    171.91844177246,
    173.82876586914,
    175.02882385254,
    176.80574035645,
    177.53170776367,
    178.50051879883,
    178.55145263672,
    178.51135253906,
    180.22477722168,
    180.88128662109,
    182.12176513672,
    183.58641052246,
    184.1174621582,
    184.54441833496,
    185.79092407227,
    187.2606048584,
    188.37274169922,
    190.17517089844,
    191.96086120605,
    192.92491149902,
    195.04859924316,
    195.88293457031,
    200.88150024414,
    200.8374786377,
    202.10246276855,
    204.18078613281,
    203.01582336426,
    204.76487731934,
    207.27696228027,
    208.69123840332,
    210.54109191895,
    214.18989562988,
    215.64587402344,
    220.69868469238,
    218.37178039551,
    212.68955993652,
    213.17874145508,
    215.1701965332])

resid = np.array([
    np.nan,
    .17000007629395,
    .09542549401522,
    -.12046983093023,
    .06584875285625,
    -.108916208148,
    .1050778105855,
    -.02409455552697,
    -.13867701590061,
    .02610917761922,
    -.02740954607725,
    -.02212125435472,
    .09219767153263,
    -.08159548044205,
    .08206310123205,
    -.02996070124209,
    -.04444679990411,
    .13324689865112,
    -.03658904880285,
    .09998744726181,
    -.09454916417599,
    -.01898162066936,
    .01479982770979,
    .0733450204134,
    .00289814220741,
    .10339243710041,
    -.04157593473792,
    .12589477002621,
    .27754187583923,
    .00605186540633,
    .23670043051243,
    -.14811079204082,
    .02656871080399,
    .1241647452116,
    .10676880925894,
    .1922178119421,
    .06467659771442,
    .25700229406357,
    .11983319371939,
    .10411861538887,
    .29096505045891,
    .14921590685844,
    .12966203689575,
    .21330565214157,
    .18424116075039,
    -.0400672107935,
    .07034146040678,
    .06367607414722,
    -.24190320074558,
    .0995594933629,
    -.11187721043825,
    -.09070245176554,
    -.07298398017883,
    -.05816079676151,
    .05424249917269,
    .14924050867558,
    .6296826004982,
    .03644690662622,
    .93531775474548,
    .59600579738617,
    .61020302772522,
    .42304915189743,
    .86550986766815,
    .34049755334854,
    -.30258512496948,
    .05361825972795,
    -.0454841889441,
    -.02936388924718,
    -.61587244272232,
    .0876636877656,
    .082030788064,
    -.0226839594543,
    .48874869942665,
    -.07856046408415,
    -.05799730122089,
    .25920879840851,
    .32747456431389,
    .6855503320694,
    .5889800786972,
    .30819287896156,
    .97135013341904,
    1.1329106092453,
    .77096086740494,
    1.2662346363068,
    1.1863652467728,
    -.17949041724205,
    .26625844836235,
    .64303278923035,
    -.03789660334587,
    .48664516210556,
    -.06969699263573,
    -.93996042013168,
    -1.1767102479935,
    .92136597633362,
    -1.1050474643707,
    -1.7186782360077,
    -.33977076411247,
    -.17556031048298,
    -.13721539080143,
    .19485920667648,
    .07558213919401,
    -.32518845796585,
    -.06432051956654,
    -.4441542327404,
    .33422181010246,
    -.30784299969673,
    -.15077359974384,
    .48158806562424,
    -2.1835913658142,
    .16163675487041,
    .04294444993138,
    .54269498586655,
    .56559586524963,
    .2857401072979,
    .34966295957565,
    .10411833971739,
    .29675936698914,
    .35986250638962,
    .41361248493195,
    .25954058766365,
    .82969123125076,
    .21246488392353,
    -.30873239040375,
    .9504047036171,
    .11538010835648,
    .31005910038948,
    1.5748337507248,
    -.25448590517044,
    -1.1003407239914,
    -.21669489145279,
    -.27065354585648,
    -.11676382273436,
    -.08707597851753,
    -.06220890209079,
    .05857050418854,
    -.039402063936,
    -.12234635651112,
    -.39269989728928,
    .07823857665062,
    -.32393181324005,
    -.06424260139465,
    .25494971871376,
    .02488171122968,
    .13047952950001,
    .31977880001068,
    -.21991983056068,
    -.07626695930958,
    .24489018321037,
    .41647511720657,
    -.13807360827923,
    .19318304955959,
    .17320497334003,
    -.54350554943085,
    -.4498653113842,
    .22847975790501,
    -.39823773503304,
    -.62833803892136,
    .27627000212669,
    -.05921940878034,
    .05722366645932,
    .45559296011925,
    -.00725806690753,
    .6016600728035,
    .31685426831245,
    .67666161060333,
    .78155589103699,
    .07122328877449,
    .57118928432465,
    -.40575236082077,
    -.1317123323679,
    -.90052020549774,
    -.85146051645279,
    .78865325450897,
    -.224773645401,
    .31871569156647,
    .47823718190193,
    -.38640683889389,
    -.41746246814728,
    .35557863116264,
    .50907653570175,
    .13939322531223,
    .72726023197174,
    .62482494115829,
    -.16085371375084,
    .87508809566498,
    -.34859669208527,
    3.3170635700226,
    -1.4815051555634,
    -.1374823898077,
    .59753465652466,
    -2.280793428421,
    .5581876039505,
    1.155125617981,
    .06103510409594,
    .44175490736961,
    1.9539065361023,
    -.19290342926979,
    2.9641311168671,
    -3.8096718788147,
    -6.1977920532227,
    -.01855664327741,
    1.2902550697327,
    1.2147966623306])

yr = np.array([
    np.nan,
    .17000007629395,
    .09542549401522,
    -.12046983093023,
    .06584875285625,
    -.108916208148,
    .1050778105855,
    -.02409455552697,
    -.13867701590061,
    .02610917761922,
    -.02740954607725,
    -.02212125435472,
    .09219767153263,
    -.08159548044205,
    .08206310123205,
    -.02996070124209,
    -.04444679990411,
    .13324689865112,
    -.03658904880285,
    .09998744726181,
    -.09454916417599,
    -.01898162066936,
    .01479982770979,
    .0733450204134,
    .00289814220741,
    .10339243710041,
    -.04157593473792,
    .12589477002621,
    .27754187583923,
    .00605186540633,
    .23670043051243,
    -.14811079204082,
    .02656871080399,
    .1241647452116,
    .10676880925894,
    .1922178119421,
    .06467659771442,
    .25700229406357,
    .11983319371939,
    .10411861538887,
    .29096505045891,
    .14921590685844,
    .12966203689575,
    .21330565214157,
    .18424116075039,
    -.0400672107935,
    .07034146040678,
    .06367607414722,
    -.24190320074558,
    .0995594933629,
    -.11187721043825,
    -.09070245176554,
    -.07298398017883,
    -.05816079676151,
    .05424249917269,
    .14924050867558,
    .6296826004982,
    .03644690662622,
    .93531775474548,
    .59600579738617,
    .61020302772522,
    .42304915189743,
    .86550986766815,
    .34049755334854,
    -.30258512496948,
    .05361825972795,
    -.0454841889441,
    -.02936388924718,
    -.61587244272232,
    .0876636877656,
    .082030788064,
    -.0226839594543,
    .48874869942665,
    -.07856046408415,
    -.05799730122089,
    .25920879840851,
    .32747456431389,
    .6855503320694,
    .5889800786972,
    .30819287896156,
    .97135013341904,
    1.1329106092453,
    .77096086740494,
    1.2662346363068,
    1.1863652467728,
    -.17949041724205,
    .26625844836235,
    .64303278923035,
    -.03789660334587,
    .48664516210556,
    -.06969699263573,
    -.93996042013168,
    -1.1767102479935,
    .92136597633362,
    -1.1050474643707,
    -1.7186782360077,
    -.33977076411247,
    -.17556031048298,
    -.13721539080143,
    .19485920667648,
    .07558213919401,
    -.32518845796585,
    -.06432051956654,
    -.4441542327404,
    .33422181010246,
    -.30784299969673,
    -.15077359974384,
    .48158806562424,
    -2.1835913658142,
    .16163675487041,
    .04294444993138,
    .54269498586655,
    .56559586524963,
    .2857401072979,
    .34966295957565,
    .10411833971739,
    .29675936698914,
    .35986250638962,
    .41361248493195,
    .25954058766365,
    .82969123125076,
    .21246488392353,
    -.30873239040375,
    .9504047036171,
    .11538010835648,
    .31005910038948,
    1.5748337507248,
    -.25448590517044,
    -1.1003407239914,
    -.21669489145279,
    -.27065354585648,
    -.11676382273436,
    -.08707597851753,
    -.06220890209079,
    .05857050418854,
    -.039402063936,
    -.12234635651112,
    -.39269989728928,
    .07823857665062,
    -.32393181324005,
    -.06424260139465,
    .25494971871376,
    .02488171122968,
    .13047952950001,
    .31977880001068,
    -.21991983056068,
    -.07626695930958,
    .24489018321037,
    .41647511720657,
    -.13807360827923,
    .19318304955959,
    .17320497334003,
    -.54350554943085,
    -.4498653113842,
    .22847975790501,
    -.39823773503304,
    -.62833803892136,
    .27627000212669,
    -.05921940878034,
    .05722366645932,
    .45559296011925,
    -.00725806690753,
    .6016600728035,
    .31685426831245,
    .67666161060333,
    .78155589103699,
    .07122328877449,
    .57118928432465,
    -.40575236082077,
    -.1317123323679,
    -.90052020549774,
    -.85146051645279,
    .78865325450897,
    -.224773645401,
    .31871569156647,
    .47823718190193,
    -.38640683889389,
    -.41746246814728,
    .35557863116264,
    .50907653570175,
    .13939322531223,
    .72726023197174,
    .62482494115829,
    -.16085371375084,
    .87508809566498,
    -.34859669208527,
    3.3170635700226,
    -1.4815051555634,
    -.1374823898077,
    .59753465652466,
    -2.280793428421,
    .5581876039505,
    1.155125617981,
    .06103510409594,
    .44175490736961,
    1.9539065361023,
    -.19290342926979,
    2.9641311168671,
    -3.8096718788147,
    -6.1977920532227,
    -.01855664327741,
    1.2902550697327,
    1.2147966623306])

mse = np.array([
    1.4469859600067,
    1.4469859600067,
    .89943557977676,
    .77543312311172,
    .72303003072739,
    .69548159837723,
    .67933452129364,
    .6692613363266,
    .66273111104965,
    .65839165449142,
    .65546035766602,
    .6534583568573,
    .65208071470261,
    .65112781524658,
    .65046626329422,
    .65000593662262,
    .64968502521515,
    .64946103096008,
    .64930462837219,
    .64919525384903,
    .64911878108978,
    .64906531572342,
    .6490278840065,
    .64900171756744,
    .6489834189415,
    .64897060394287,
    .64896160364151,
    .64895534515381,
    .6489509344101,
    .64894789457321,
    .64894568920135,
    .64894419908524,
    .64894318580627,
    .64894241094589,
    .64894187450409,
    .64894151687622,
    .64894127845764,
    .64894109964371,
    .64894098043442,
    .64894092082977,
    .64894086122513,
    .64894080162048,
    .64894074201584,
    .64894074201584,
    .64894074201584,
    .64894074201584,
    .64894074201584,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119,
    .64894068241119])

stdp = np.array([
    .88084751367569,
    .88084751367569,
    .65303039550781,
    .55365419387817,
    .45908725261688,
    .42810925841331,
    .37837743759155,
    .37686342000961,
    .35719576478004,
    .3220648765564,
    .31943875551224,
    .30907514691353,
    .30120712518692,
    .31383177638054,
    .29652059078217,
    .30856171250343,
    .30095273256302,
    .29171526432037,
    .31331890821457,
    .30463594198227,
    .31990340352058,
    .30126947164536,
    .29703867435455,
    .29884466528893,
    .31037190556526,
    .30912432074547,
    .32505416870117,
    .31537705659866,
    .33494210243225,
    .37874156236649,
    .37366089224815,
    .40859284996986,
    .37640652060509,
    .37692713737488,
    .39422073960304,
    .40755322575569,
    .43472331762314,
    .43878075480461,
    .47569087147713,
    .48725643754005,
    .49617394804955,
    .53683114051819,
    .55128628015518,
    .56243091821671,
    .58791494369507,
    .60756206512451,
    .58892780542374,
    .59145200252533,
    .59339815378189,
    .54422444105148,
    .55698639154434,
    .53304374217987,
    .51458370685577,
    .50035130977631,
    .48937830328941,
    .49780988693237,
    .52120143175125,
    .62369203567505,
    .6182547211647,
    .76608312129974,
    .84627467393875,
    .92499214410782,
    .96879118680954,
    1.0870156288147,
    1.1105998754501,
    1.0274360179901,
    1.013991355896,
    .98673474788666,
    .96571969985962,
    .84817039966583,
    .85888928174973,
    .86715340614319,
    .85663330554962,
    .93297851085663,
    .90738350152969,
    .88765007257462,
    .92311006784439,
    .96734017133713,
    1.0690053701401,
    1.1473876237869,
    1.1740373373032,
    1.3128218650818,
    1.4704967737198,
    1.5582785606384,
    1.7273052930832,
    1.8745132684708,
    1.7853132486343,
    1.7841064929962,
    1.850741147995,
    1.800768494606,
    1.8466963768005,
    1.7976499795914,
    1.6078149080276,
    1.3938897848129,
    1.5498898029327,
    1.3492304086685,
    1.059396147728,
    1.0217411518097,
    1.0096007585526,
    1.0002405643463,
    1.0436969995499,
    1.0603114366531,
    1.0055546760559,
    .99712115526199,
    .92305397987366,
    .9841884970665,
    .92997401952744,
    .90506774187088,
    .9872123003006,
    .61137217283249,
    .65943044424057,
    .67959040403366,
    .77959072589874,
    .87357920408249,
    .91226226091385,
    .95897603034973,
    .96120971441269,
    .99671375751495,
    1.0409790277481,
    1.0919979810715,
    1.1144404411316,
    1.2330915927887,
    1.2401138544083,
    1.161071896553,
    1.3028255701065,
    1.2938764095306,
    1.3207612037659,
    1.5610725879669,
    1.4760913848877,
    1.258552312851,
    1.2090681791306,
    1.1540271043777,
    1.12848341465,
    1.1087870597839,
    1.0936040878296,
    1.0987877845764,
    1.0858948230743,
    1.0590622425079,
    .98770052194595,
    1.0002481937408,
    .94235575199127,
    .93150353431702,
    .97381073236465,
    .9726470708847,
    .98864215612411,
    1.0347559452057,
    .98585307598114,
    .96503925323486,
    .9996662735939,
    1.0601476430893,
    1.022319316864,
    1.043828368187,
    1.0604115724564,
    .95495897531509,
    .87365657091141,
    .91232192516327,
    .84078407287598,
    .73495537042618,
    .78849309682846,
    .77909576892853,
    .78874284029007,
    .8637443780899,
    .8540056347847,
    .94784545898438,
    .98641014099121,
    1.0837067365646,
    1.1925053596497,
    1.1750392913818,
    1.2460317611694,
    1.1487410068512,
    1.1075156927109,
    .94060403108597,
    .7950227856636,
    .93615245819092,
    .89293897151947,
    .94407802820206,
    1.0172899961472,
    .93860250711441,
    .86104601621628,
    .91948908567429,
    .99833220243454,
    1.008442401886,
    1.1175880432129,
    1.2017351388931,
    1.1483734846115,
    1.2761443853378,
    1.188849568367,
    1.7296310663223,
    1.4202431440353,
    1.3675138950348,
    1.445098400116,
    1.031960606575,
    1.1313284635544,
    1.3214453458786,
    1.3112732172012,
    1.367110490799,
    1.674845457077,
    1.5979281663895,
    2.064112663269,
    1.3536450862885,
    .30015936493874,
    .36831066012383,
    .64060544967651])

icstats = np.array([
    202,
    np.nan,
    -243.77512585356,
    3,
    493.55025170713,
    503.47505479933])


results = Bunch(
    llf=llf,
    nobs=nobs,
    k=k,
    k_exog=k_exog,
    sigma=sigma,
    chi2=chi2,
    df_model=df_model,
    k_ar=k_ar,
    k_ma=k_ma,
    params=params,
    cov_params=cov_params,
    xb=xb,
    y=y,
    resid=resid,
    yr=yr,
    mse=mse,
    stdp=stdp,
    icstats=icstats
)
