import numpy as np

from statsmodels.tools.tools import Bunch

llf = np.array([-240.79351748413])

nobs = np.array([202])

k = np.array([4])

k_exog = np.array([1])

sigma = np.array([.79349479007541])

chi2 = np.array([31643.977146904])

df_model = np.array([3])

k_ar = np.array([1])

k_ma = np.array([2])

params = np.array([
    .99502750401315,
    -.68630179255403,
    -.19840894739396,
    .79349479007541])

cov_params = np.array([
    .00010016992219,
    -.00021444523598,
    -.00023305572854,
    -6.768123591e-06,
    -.00021444523598,
    .00104186449549,
    .00023669747281,
    3.902897504e-06,
    -.00023305572854,
    .00023669747281,
    .0010810935718,
    .00020764808165,
    -6.768123591e-06,
    3.902897504e-06,
    .00020764808165,
    .0002668504612]).reshape(4, 4)

xb = np.array([
    0,
    0,
    .11361486464739,
    .14230862259865,
    .07256115227938,
    .13274206221104,
    .06747215241194,
    .13822889328003,
    .09585004299879,
    .06099047139287,
    .10120190680027,
    .07761032879353,
    .07942545413971,
    .11177492141724,
    .06088993698359,
    .11208334565163,
    .0755797252059,
    .07422959059477,
    .12350624799728,
    .07623053342104,
    .12391770631075,
    .06531815230846,
    .08897981792688,
    .09103457629681,
    .10980048775673,
    .09255626052618,
    .12732291221619,
    .08764986693859,
    .14262486994267,
    .19330270588398,
    .13410428166389,
    .2202503234148,
    .1138079687953,
    .17364008724689,
    .19471970200539,
    .20120698213577,
    .24072274565697,
    .21839858591557,
    .29251739382744,
    .26838713884354,
    .28637075424194,
    .3556769490242,
    .33624804019928,
    .35650280117989,
    .3974232673645,
    .40968126058578,
    .36446389555931,
    .40641778707504,
    .40639826655388,
    .32208651304245,
    .41636437177658,
    .34307014942169,
    .3511538207531,
    .34216031432152,
    .33759200572968,
    .36354607343674,
    .39148297905922,
    .55032896995544,
    .4113195836544,
    .7244918346405,
    .67152947187424,
    .76787060499191,
    .77276849746704,
    .96944856643677,
    .88270664215088,
    .7563271522522,
    .86404490470886,
    .82250237464905,
    .83520317077637,
    .65044301748276,
    .83044308423996,
    .79827356338501,
    .78103590011597,
    .93702721595764,
    .78709679841995,
    .81435388326645,
    .89593154191971,
    .92867535352707,
    1.0709822177887,
    1.0957812070847,
    1.0792914628983,
    1.3286831378937,
    1.4503024816513,
    1.4619816541672,
    1.7190475463867,
    1.8096150159836,
    1.5324629545212,
    1.721804857254,
    1.8408879041672,
    1.6955831050873,
    1.8928952217102,
    1.7459137439728,
    1.5055395364761,
    1.3664853572845,
    1.8893030881882,
    1.256967663765,
    1.0567245483398,
    1.2921603918076,
    1.2266329526901,
    1.2085332870483,
    1.275726556778,
    1.2278587818146,
    1.1046848297119,
    1.1517647504807,
    .99646359682083,
    1.194694519043,
    .97580307722092,
    1.0148292779922,
    1.1635760068893,
    .35167038440704,
    .95728904008865,
    .78414303064346,
    .95968008041382,
    .97746151685715,
    .94291216135025,
    .99327826499939,
    .93940645456314,
    1.013852596283,
    1.0454497337341,
    1.0929356813431,
    1.0810794830322,
    1.2874436378479,
    1.1533098220825,
    1.0470397472382,
    1.4171674251556,
    1.1959022283554,
    1.3181202411652,
    1.7197531461716,
    1.2677561044693,
    1.0768386125565,
    1.2508004903793,
    1.1625586748123,
    1.1872273683548,
    1.1668027639389,
    1.1576874256134,
    1.1782459020615,
    1.1398378610611,
    1.1065219640732,
    1.0032601356506,
    1.1087976694107,
    .95788156986237,
    1.0163568258286,
    1.079482793808,
    1.0131409168243,
    1.0506906509399,
    1.1052004098892,
    .95601671934128,
    .99452114105225,
    1.0641269683838,
    1.1217628717422,
    .98107707500458,
    1.0877858400345,
    1.0735836029053,
    .86890149116516,
    .86449563503265,
    1.0060983896255,
    .79812264442444,
    .70991164445877,
    .91461282968521,
    .78625136613846,
    .8291689157486,
    .93680161237717,
    .81633454561234,
    1.0196126699448,
    .95442569255829,
    1.1131925582886,
    1.1916073560715,
    1.05200278759,
    1.2451642751694,
    .97296446561813,
    1.0647999048233,
    .78715896606445,
    .74267995357513,
    1.1400059461594,
    .82839399576187,
    1.0262999534607,
    1.0628409385681,
    .84051495790482,
    .82304340600967,
    1.0028872489929,
    1.0457111597061,
    .97847640514374,
    1.1855980157852,
    1.195351600647,
    1.0270363092422,
    1.3610677719116,
    1.0189098119736,
    2.1800265312195,
    .86722087860107,
    1.3893752098083,
    1.4851142168045,
    .65110164880753,
    1.417050242424,
    1.4938380718231,
    1.2786860466003,
    1.446773648262,
    1.9284181594849,
    1.4071846008301,
    2.4745123386383,
    .53088372945786,
    -.25887301564217,
    1.0166070461273,
    1.1028108596802])

y = np.array([
    np.nan,
    28.979999542236,
    29.263614654541,
    29.492309570313,
    29.442562103271,
    29.672742843628,
    29.617471694946,
    29.888229370117,
    29.935850143433,
    29.870990753174,
    30.021202087402,
    30.057609558105,
    30.119426727295,
    30.321773529053,
    30.280889511108,
    30.492082595825,
    30.515581130981,
    30.554229736328,
    30.813507080078,
    30.826231002808,
    31.063919067383,
    31.015319824219,
    31.108980178833,
    31.21103477478,
    31.389801025391,
    31.472555160522,
    31.707323074341,
    31.737649917603,
    32.022624969482,
    32.473300933838,
    32.584106445313,
    33.070247650146,
    33.013809204102,
    33.273639678955,
    33.594722747803,
    33.901206970215,
    34.340721130371,
    34.61840057373,
    35.192520141602,
    35.568386077881,
    35.98637008667,
    36.65567779541,
    37.136245727539,
    37.65650177002,
    38.297424316406,
    38.909679412842,
    39.264465332031,
    39.806419372559,
    40.306400299072,
    40.42208480835,
    41.016361236572,
    41.243072509766,
    41.551155090332,
    41.84215927124,
    42.137592315674,
    42.563545227051,
    43.091484069824,
    44.250328063965,
    44.611320495605,
    46.324489593506,
    47.471527099609,
    48.867870330811,
    50.072769165039,
    51.9694480896,
    53.182704925537,
    53.756328582764,
    54.864044189453,
    55.722503662109,
    56.635204315186,
    56.750442504883,
    57.830444335938,
    58.698276519775,
    59.481037139893,
    60.937026977539,
    61.587097167969,
    62.414352416992,
    63.595932006836,
    64.828674316406,
    66.570983886719,
    68.195777893066,
    69.579292297363,
    71.928680419922,
    74.450302124023,
    76.661979675293,
    79.719047546387,
    82.709617614746,
    84.132461547852,
    86.421798706055,
    89.040885925293,
    90.79557800293,
    93.39289855957,
    95.14591217041,
    95.905540466309,
    96.366485595703,
    99.389305114746,
    99.356964111328,
    98.956726074219,
    100.09216308594,
    101.02663421631,
    102.00853729248,
    103.37572479248,
    104.52786254883,
    105.20468139648,
    106.25176239014,
    106.69645690918,
    108.19469451904,
    108.67579650879,
    109.51483154297,
    111.06357574463,
    109.05166625977,
    110.45729064941,
    110.98413848877,
    112.35968017578,
    113.6774597168,
    114.74291229248,
    115.99327850342,
    116.93940734863,
    118.21385192871,
    119.54544830322,
    120.99293518066,
    122.28107452393,
    124.38744354248,
    125.65331268311,
    126.44704437256,
    128.91716003418,
    130.09590148926,
    131.81811523438,
    135.11975097656,
    135.96775817871,
    136.17684936523,
    137.45079040527,
    138.36254882813,
    139.48722839355,
    140.56680297852,
    141.65768432617,
    142.87825012207,
    143.93983459473,
    144.9065246582,
    145.50326538086,
    146.70880126953,
    147.25788879395,
    148.21635437012,
    149.47947692871,
    150.41313171387,
    151.55068969727,
    152.90519714355,
    153.55603027344,
    154.49452209473,
    155.76412963867,
    157.22177124023,
    157.98107910156,
    159.28778076172,
    160.47357177734,
    160.76889038086,
    161.26449584961,
    162.50610351563,
    162.7981262207,
    162.90991210938,
    164.11460876465,
    164.6862487793,
    165.5291595459,
    166.83679199219,
    167.5163269043,
    169.11961364746,
    170.25442504883,
    172.01318359375,
    173.8916015625,
    174.95199584961,
    176.84516906738,
    177.37295532227,
    178.46479797363,
    178.38716125488,
    178.44267272949,
    180.44000244141,
    180.8283996582,
    182.22630310059,
    183.66284179688,
    184.04051208496,
    184.52304077148,
    185.90287780762,
    187.34571838379,
    188.37846374512,
    190.28559875488,
    191.99536132813,
    192.82704162598,
    195.16107177734,
    195.71890258789,
    201.3800201416,
    200.26721191406,
    202.08937072754,
    204.18510437012,
    202.55110168457,
    204.99105834961,
    207.41383361816,
    208.61668395996,
    210.57977294922,
    214.4234161377,
    215.40417480469,
    221.08451843262,
    217.41989135742,
    211.91511535645,
    213.68760681152,
    215.57180786133])

resid = np.array([
    np.nan,
    .17000007629395,
    .08638589829206,
    -.12230817228556,
    .09743892401457,
    -.12274374067783,
    .13252860307693,
    -.04822873324156,
    -.12585072219372,
    .04901013895869,
    -.04120244085789,
    -.01760895550251,
    .0905727148056,
    -.1017746925354,
    .09910991042852,
    -.05208196863532,
    -.03558072075248,
    .13577139377594,
    -.0635067820549,
    .11377000063658,
    -.11391747742891,
    .00468154205009,
    .01102056354284,
    .0689652711153,
    -.00980201456696,
    .10744450241327,
    -.05732321739197,
    .14234967529774,
    .25737473368645,
    -.02330071851611,
    .26589342951775,
    -.17024727165699,
    .08618897944689,
    .12636296451092,
    .10527954250574,
    .19879072904587,
    .05928030610085,
    .28160139918327,
    .10748030990362,
    .13161440193653,
    .31362771987915,
    .14432306587696,
    .16375194489956,
    .24349948763847,
    .20257519185543,
    -.00967973750085,
    .13553610444069,
    .09358221292496,
    -.20640131831169,
    .17791347205639,
    -.11636133491993,
    -.04307091236115,
    -.05115458369255,
    -.04216108098626,
    .06240950897336,
    .13645392656326,
    .60851699113846,
    -.05032894387841,
    .98867815732956,
    .47550892829895,
    .62846976518631,
    .43213015794754,
    .92723226547241,
    .33055067062378,
    -.18270587921143,
    .24367287755013,
    .0359565988183,
    .07749536633492,
    -.53520393371582,
    .24955849349499,
    .0695584192872,
    .00172565307003,
    .51896333694458,
    -.13702796399593,
    .01290242280811,
    .28564843535423,
    .30406919121742,
    .67132312059402,
    .5290162563324,
    .30422034859657,
    1.0207070112228,
    1.0713183879852,
    .74969446659088,
    1.3380213975906,
    1.1809539794922,
    -.10961806029081,
    .56753551959991,
    .77819520235062,
    .05911364778876,
    .70441842079163,
    .0071063125506,
    -.74591380357742,
    -.90554106235504,
    1.1335146427155,
    -1.2893046140671,
    -1.4569646120071,
    -.15672297775745,
    -.29216033220291,
    -.22663290798664,
    .09146212786436,
    -.0757219940424,
    -.42786338925362,
    -.10468477010727,
    -.55176627635956,
    .3035394847393,
    -.49469754099846,
    -.1758000254631,
    .38517218828201,
    -2.3635804653168,
    .44833266735077,
    -.25729209184647,
    .41586154699326,
    .34031534194946,
    .1225445792079,
    .25708478689194,
    .00672172475606,
    .26059049367905,
    .28615045547485,
    .35455179214478,
    .2070597410202,
    .81892204284668,
    .11255792528391,
    -.25330832600594,
    1.0529587268829,
    -.0171735137701,
    .40410390496254,
    1.5818736553192,
    -.41975006461143,
    -.86774694919586,
    .02315225638449,
    -.25080046057701,
    -.06255260109901,
    -.08723650127649,
    -.06679660826921,
    .04230948910117,
    -.07823982834816,
    -.13983787596226,
    -.40652501583099,
    .09674594551325,
    -.40880066156387,
    -.05788768827915,
    .1836401373148,
    -.07948285341263,
    .08686522394419,
    .24931237101555,
    -.30519738793373,
    -.05602284148335,
    .20547580718994,
    .33588215708733,
    -.22176894545555,
    .21891987323761,
    .11221113055944,
    -.57358360290527,
    -.36890152096748,
    .23551045358181,
    -.50609844923019,
    -.59812569618225,
    .29008835554123,
    -.21461589634418,
    .013751687482,
    .37082800269127,
    -.13679853081703,
    .5836746096611,
    .18038421869278,
    .64556515216827,
    .68681055307388,
    .00838960427791,
    .64800941944122,
    -.44517654180527,
    .02703555487096,
    -.8647877573967,
    -.68716812133789,
    .85732614994049,
    -.44000896811485,
    .37160298228264,
    .37370926141739,
    -.46285009384155,
    -.34051498770714,
    .37695357203484,
    .3971218764782,
    .05427964404225,
    .72153580188751,
    .51439893245697,
    -.19535164535046,
    .97296363115311,
    -.46107393503189,
    3.4810900688171,
    -1.9800295829773,
    .43278217315674,
    .61062479019165,
    -2.2851173877716,
    1.0229095220566,
    .92894279956818,
    -.07583882659674,
    .51631212234497,
    1.9152258634567,
    -.42641922831535,
    3.2058219909668,
    -4.1955056190491,
    -5.2458953857422,
    .75588232278824,
    .7813817858696,
    .81318950653076])

yr = np.array([
    np.nan,
    .17000007629395,
    .08638589829206,
    -.12230817228556,
    .09743892401457,
    -.12274374067783,
    .13252860307693,
    -.04822873324156,
    -.12585072219372,
    .04901013895869,
    -.04120244085789,
    -.01760895550251,
    .0905727148056,
    -.1017746925354,
    .09910991042852,
    -.05208196863532,
    -.03558072075248,
    .13577139377594,
    -.0635067820549,
    .11377000063658,
    -.11391747742891,
    .00468154205009,
    .01102056354284,
    .0689652711153,
    -.00980201456696,
    .10744450241327,
    -.05732321739197,
    .14234967529774,
    .25737473368645,
    -.02330071851611,
    .26589342951775,
    -.17024727165699,
    .08618897944689,
    .12636296451092,
    .10527954250574,
    .19879072904587,
    .05928030610085,
    .28160139918327,
    .10748030990362,
    .13161440193653,
    .31362771987915,
    .14432306587696,
    .16375194489956,
    .24349948763847,
    .20257519185543,
    -.00967973750085,
    .13553610444069,
    .09358221292496,
    -.20640131831169,
    .17791347205639,
    -.11636133491993,
    -.04307091236115,
    -.05115458369255,
    -.04216108098626,
    .06240950897336,
    .13645392656326,
    .60851699113846,
    -.05032894387841,
    .98867815732956,
    .47550892829895,
    .62846976518631,
    .43213015794754,
    .92723226547241,
    .33055067062378,
    -.18270587921143,
    .24367287755013,
    .0359565988183,
    .07749536633492,
    -.53520393371582,
    .24955849349499,
    .0695584192872,
    .00172565307003,
    .51896333694458,
    -.13702796399593,
    .01290242280811,
    .28564843535423,
    .30406919121742,
    .67132312059402,
    .5290162563324,
    .30422034859657,
    1.0207070112228,
    1.0713183879852,
    .74969446659088,
    1.3380213975906,
    1.1809539794922,
    -.10961806029081,
    .56753551959991,
    .77819520235062,
    .05911364778876,
    .70441842079163,
    .0071063125506,
    -.74591380357742,
    -.90554106235504,
    1.1335146427155,
    -1.2893046140671,
    -1.4569646120071,
    -.15672297775745,
    -.29216033220291,
    -.22663290798664,
    .09146212786436,
    -.0757219940424,
    -.42786338925362,
    -.10468477010727,
    -.55176627635956,
    .3035394847393,
    -.49469754099846,
    -.1758000254631,
    .38517218828201,
    -2.3635804653168,
    .44833266735077,
    -.25729209184647,
    .41586154699326,
    .34031534194946,
    .1225445792079,
    .25708478689194,
    .00672172475606,
    .26059049367905,
    .28615045547485,
    .35455179214478,
    .2070597410202,
    .81892204284668,
    .11255792528391,
    -.25330832600594,
    1.0529587268829,
    -.0171735137701,
    .40410390496254,
    1.5818736553192,
    -.41975006461143,
    -.86774694919586,
    .02315225638449,
    -.25080046057701,
    -.06255260109901,
    -.08723650127649,
    -.06679660826921,
    .04230948910117,
    -.07823982834816,
    -.13983787596226,
    -.40652501583099,
    .09674594551325,
    -.40880066156387,
    -.05788768827915,
    .1836401373148,
    -.07948285341263,
    .08686522394419,
    .24931237101555,
    -.30519738793373,
    -.05602284148335,
    .20547580718994,
    .33588215708733,
    -.22176894545555,
    .21891987323761,
    .11221113055944,
    -.57358360290527,
    -.36890152096748,
    .23551045358181,
    -.50609844923019,
    -.59812569618225,
    .29008835554123,
    -.21461589634418,
    .013751687482,
    .37082800269127,
    -.13679853081703,
    .5836746096611,
    .18038421869278,
    .64556515216827,
    .68681055307388,
    .00838960427791,
    .64800941944122,
    -.44517654180527,
    .02703555487096,
    -.8647877573967,
    -.68716812133789,
    .85732614994049,
    -.44000896811485,
    .37160298228264,
    .37370926141739,
    -.46285009384155,
    -.34051498770714,
    .37695357203484,
    .3971218764782,
    .05427964404225,
    .72153580188751,
    .51439893245697,
    -.19535164535046,
    .97296363115311,
    -.46107393503189,
    3.4810900688171,
    -1.9800295829773,
    .43278217315674,
    .61062479019165,
    -2.2851173877716,
    1.0229095220566,
    .92894279956818,
    -.07583882659674,
    .51631212234497,
    1.9152258634567,
    -.42641922831535,
    3.2058219909668,
    -4.1955056190491,
    -5.2458953857422,
    .75588232278824,
    .7813817858696,
    .81318950653076])

mse = np.array([
    1.4407075643539,
    1.4407075643539,
    .79720854759216,
    .75209444761276,
    .71109557151794,
    .68920749425888,
    .67417079210281,
    .66374856233597,
    .65616118907928,
    .65050256252289,
    .64619332551956,
    .64286160469055,
    .64025497436523,
    .63819670677185,
    .63655960559845,
    .6352499127388,
    .63419729471207,
    .63334810733795,
    .63266098499298,
    .63210368156433,
    .63165074586868,
    .63128209114075,
    .63098156452179,
    .63073641061783,
    .63053613901138,
    .63037252426147,
    .6302387714386,
    .63012927770615,
    .63003975152969,
    .62996637821198,
    .62990635633469,
    .62985718250275,
    .62981688976288,
    .62978386878967,
    .62975686788559,
    .62973469495773,
    .62971651554108,
    .62970167398453,
    .62968945503235,
    .62967944145203,
    .62967127561569,
    .62966454029083,
    .62965905666351,
    .62965452671051,
    .62965083122253,
    .62964779138565,
    .62964528799057,
    .62964326143265,
    .62964159250259,
    .62964022159576,
    .62963908910751,
    .62963819503784,
    .62963742017746,
    .62963682413101,
    .62963628768921,
    .6296358704567,
    .62963551282883,
    .62963527441025,
    .62963503599167,
    .62963485717773,
    .6296346783638,
    .62963455915451,
    .62963443994522,
    .62963438034058,
    .62963432073593,
    .62963426113129,
    .62963420152664,
    .629634141922,
    .629634141922,
    .62963408231735,
    .62963408231735,
    .62963408231735,
    .62963402271271,
    .62963402271271,
    .62963402271271,
    .62963402271271,
    .62963402271271,
    .62963402271271,
    .62963402271271,
    .62963402271271,
    .62963402271271,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806,
    .62963396310806])

icstats = np.array([
    202,
    np.nan,
    -240.79351748413,
    4,
    489.58703496826,
    502.82010575786])


results = Bunch(
    llf=llf,
    nobs=nobs,
    k=k,
    k_exog=k_exog,
    sigma=sigma,
    chi2=chi2,
    df_model=df_model,
    k_ar=k_ar,
    k_ma=k_ma,
    params=params,
    cov_params=cov_params,
    xb=xb,
    y=y,
    resid=resid,
    yr=yr,
    mse=mse,
    icstats=icstats
)
