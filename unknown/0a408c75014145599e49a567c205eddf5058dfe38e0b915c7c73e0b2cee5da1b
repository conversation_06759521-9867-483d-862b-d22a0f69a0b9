import numpy as np

from statsmodels.tools.tools import Bunch

llf = np.array([-242.06033399744])

nobs = np.array([202])

k = np.array([4])

k_exog = np.array([1])

sigma = np.array([.80201496146073])

chi2 = np.array([348.43324197088])

df_model = np.array([2])

k_ar = np.array([1])

k_ma = np.array([1])

params = np.array([
    .82960638524364,
    .93479332833705,
    -.75728342544279,
    .64322799840686])

cov_params = np.array([
    .14317811930738,
    -.01646077810033,
    .01510986837498,
    -.00280799533479,
    -.01646077810033,
    .00321032468661,
    -.00353027620719,
    .00097645385252,
    .01510986837498,
    -.00353027620719,
    .00484312817753,
    -.00112050648944,
    -.00280799533479,
    .00097645385252,
    -.00112050648944,
    .0007715609499]).reshape(4, 4)

xb = np.array([
    .82960641384125,
    .82960641384125,
    .697261095047,
    .61113905906677,
    .51607495546341,
    .47362637519836,
    .41342103481293,
    .40238001942635,
    .37454023957253,
    .33222004771233,
    .32514902949333,
    .31093680858612,
    .30019253492355,
    .31159669160843,
    .29182952642441,
    .30349296331406,
    .29457464814186,
    .28427124023438,
    .30664679408073,
    .29696446657181,
    .31270903348923,
    .29268020391464,
    .28816330432892,
    .29006817936897,
    .30216124653816,
    .30066826939583,
    .31728908419609,
    .30679926276207,
    .3272570669651,
    .37292611598969,
    .36668366193771,
    .40278288722038,
    .36799272894859,
    .36827209591866,
    .38623574376106,
    .39983862638474,
    .42789059877396,
    .43138384819031,
    .46953064203262,
    .48066720366478,
    .48910140991211,
    .53098994493484,
    .54496067762375,
    .55554050207138,
    .58130383491516,
    .60081332921982,
    .58008605241776,
    .58214038610458,
    .58369606733322,
    .53162068128586,
    .54543834924698,
    .52040082216263,
    .50143963098526,
    .48708060383797,
    .47620677947998,
    .48572361469269,
    .51068127155304,
    .61833620071411,
    .61110657453537,
    .76539021730423,
    .84672522544861,
    .92606955766678,
    .96840506792068,
    1.0892199277878,
    1.1097067594528,
    1.0187155008316,
    1.0030621290207,
    .97345739603043,
    .95103752613068,
    .82755368947983,
    .84054774045944,
    .85038793087006,
    .84008830785751,
    .92104357481003,
    .89359468221664,
    .87280809879303,
    .91032028198242,
    .95647835731506,
    1.0624366998672,
    1.1426770687103,
    1.1679404973984,
    1.311328291893,
    1.473167181015,
    1.5602221488953,
    1.7326545715332,
    1.8809853792191,
    1.7803012132645,
    1.7750589847565,
    1.8420933485031,
    1.7863517999649,
    1.8328944444656,
    1.7793855667114,
    1.5791050195694,
    1.3564316034317,
    1.5250737667084,
    1.3155146837234,
    1.014811873436,
    .98235523700714,
    .97552710771561,
    .97035628557205,
    1.0196926593781,
    1.0393049716949,
    .98315137624741,
    .97613000869751,
    .89980864524841,
    .96626943349838,
    .91009211540222,
    .88530200719833,
    .97303456068039,
    .57794612646103,
    .63377332687378,
    .65829831361771,
    .76562696695328,
    .86465454101563,
    .90414637327194,
    .95180231332779,
    .95238989591599,
    .98833626508713,
    1.0333099365234,
    1.0851185321808,
    1.1066001653671,
    1.2293750047684,
    1.233595252037,
    1.1480363607407,
    1.2962552309036,
    1.2842413187027,
    1.3106474876404,
    1.5614050626755,
    1.4672855138779,
    1.2362524271011,
    1.1855486631393,
    1.1294020414352,
    1.1046353578568,
    1.0858771800995,
    1.0716745853424,
    1.0786685943604,
    1.0662157535553,
    1.0390332937241,
    .96519494056702,
    .9802839756012,
    .92070508003235,
    .91108840703964,
    .95705932378769,
    .95637094974518,
    .97360169887543,
    1.0221517086029,
    .9701629281044,
    .94854199886322,
    .98542231321335,
    1.048855304718,
    1.0081344842911,
    1.0305507183075,
    1.0475262403488,
    .93612504005432,
    .85176283121109,
    .89438372850418,
    .820152759552,
    .71068543195724,
    .76979607343674,
    .76130604743958,
    .77262878417969,
    .85220617055893,
    .84146595001221,
    .93983960151672,
    .97883212566376,
    1.0793634653091,
    1.1909983158112,
    1.1690304279327,
    1.2411522865295,
    1.1360056400299,
    1.0918840169907,
    .9164656996727,
    .76586949825287,
    .918093085289,
    .87360894680023,
    .92867678403854,
    1.00588285923,
    .92233866453171,
    .84132260084152,
    .90422683954239,
    .9873673915863,
    .99707210063934,
    1.1109310388565,
    1.1971517801285,
    1.138188958168,
    1.2710473537445,
    1.1763968467712,
    1.7437561750412,
    1.4101150035858,
    1.3527159690857,
    1.4335050582886,
    .99765706062317,
    1.1067585945129,
    1.3086627721786,
    1.2968333959579,
    1.3547962903976,
    1.6768488883972,
    1.5905654430389,
    2.0774590969086,
    1.3218278884888,
    .21813294291496,
    .30750840902328,
    .60612773895264])

y = np.array([
    np.nan,
    29.809606552124,
    29.847261428833,
    29.961139678955,
    29.886075973511,
    30.013628005981,
    29.96342086792,
    30.152379989624,
    30.214540481567,
    30.142219543457,
    30.245149612427,
    30.290935516357,
    30.3401927948,
    30.521595001221,
    30.511829376221,
    30.683492660522,
    30.734575271606,
    30.764270782471,
    30.996646881104,
    31.046964645386,
    31.252710342407,
    31.242681503296,
    31.308164596558,
    31.410068511963,
    31.582162857056,
    31.680667877197,
    31.897289276123,
    31.956798553467,
    32.207256317139,
    32.652923583984,
    32.8166847229,
    33.252780914307,
    33.267993927002,
    33.468269348145,
    33.786235809326,
    34.099838256836,
    34.527889251709,
    34.831386566162,
    35.369533538818,
    35.780666351318,
    36.189102172852,
    36.830989837646,
    37.344959259033,
    37.855541229248,
    38.481304168701,
    39.100814819336,
    39.480087280273,
    39.9821434021,
    40.483695983887,
    40.631618499756,
    41.145435333252,
    41.420402526855,
    41.701438903809,
    41.987079620361,
    42.276206970215,
    42.685726165771,
    43.210681915283,
    44.318336486816,
    44.811107635498,
    46.365386962891,
    47.646724700928,
    49.026069641113,
    50.268405914307,
    52.089218139648,
    53.409706115723,
    54.018714904785,
    55.003063201904,
    55.873458862305,
    56.751037597656,
    56.927551269531,
    57.840549468994,
    58.750389099121,
    59.540088653564,
    60.921043395996,
    61.693592071533,
    62.472805023193,
    63.610321044922,
    64.856483459473,
    66.562438964844,
    68.24267578125,
    69.667938232422,
    71.911323547363,
    74.473167419434,
    76.760215759277,
    79.732650756836,
    82.780990600586,
    84.380302429199,
    86.475059509277,
    89.042091369629,
    90.886352539063,
    93.332893371582,
    95.179389953613,
    95.979103088379,
    96.356430053711,
    99.02507019043,
    99.415512084961,
    98.914810180664,
    99.782356262207,
    100.7755279541,
    101.770362854,
    103.11968994141,
    104.33930969238,
    105.083152771,
    106.07612609863,
    106.59980773926,
    107.96627044678,
    108.61009216309,
    109.38529968262,
    110.87303924561,
    109.27794647217,
    110.13377380371,
    110.85829162598,
    112.16562652588,
    113.56465148926,
    114.70414733887,
    115.95180511475,
    116.95239257813,
    118.188331604,
    119.53330993652,
    120.98512268066,
    122.30659484863,
    124.3293762207,
    125.73359680176,
    126.54803466797,
    128.79624938965,
    130.18423461914,
    131.81065368652,
    134.96139526367,
    136.16728210449,
    136.33625793457,
    137.38554382324,
    138.32939147949,
    139.40463256836,
    140.48587036133,
    141.57167053223,
    142.77867126465,
    143.86622619629,
    144.83903503418,
    145.46519470215,
    146.58029174805,
    147.220703125,
    148.11108398438,
    149.35705566406,
    150.35636901855,
    151.47360229492,
    152.82215881348,
    153.5701751709,
    154.44854736328,
    155.68542480469,
    157.14886474609,
    158.00813293457,
    159.23054504395,
    160.44752502441,
    160.83612060547,
    161.25175476074,
    162.39437866211,
    162.82015991211,
    162.91067504883,
    163.96978759766,
    164.66130065918,
    165.47262573242,
    166.75219726563,
    167.54145812988,
    169.03984069824,
    170.27883911133,
    171.9793548584,
    173.89099121094,
    175.06903076172,
    176.84115600586,
    177.5359954834,
    178.49188232422,
    178.5164642334,
    178.46586608887,
    180.21809387207,
    180.8736114502,
    182.12867736816,
    183.60589599609,
    184.12232971191,
    184.54132080078,
    185.80421447754,
    187.28736877441,
    188.39706420898,
    190.2109375,
    191.99716186523,
    192.93818664551,
    195.07104492188,
    195.8763885498,
    200.94375610352,
    200.81010437012,
    202.05271911621,
    204.13349914551,
    202.89764404297,
    204.68077087402,
    207.22866821289,
    208.63482666016,
    210.48779296875,
    214.17184448242,
    215.58755493164,
    220.68745422363,
    218.21083068848,
    212.39213562012,
    212.978515625,
    215.07511901855])

resid = np.array([
    np.nan,
    -.6596063375473,
    -.49726036190987,
    -.5911386013031,
    -.34607490897179,
    -.46362805366516,
    -.21342028677464,
    -.31237986683846,
    -.40454092621803,
    -.22221945226192,
    -.26514956355095,
    -.2509354352951,
    -.13019436597824,
    -.30159646272659,
    -.1318296790123,
    -.24349159002304,
    -.25457563996315,
    -.07427024841309,
    -.24664734303951,
    -.10696394741535,
    -.30270880460739,
    -.22268049418926,
    -.18816292285919,
    -.13006833195686,
    -.20216277241707,
    -.10066751390696,
    -.24728938937187,
    -.07679972797632,
    .07274255156517,
    -.20292413234711,
    .03331403434277,
    -.35277983546257,
    -.16799576580524,
    -.06826904416084,
    -.08623649924994,
    .00015908146452,
    -.12788754701614,
    .06861615926027,
    -.06953293830156,
    -.08066567778587,
    .11089706420898,
    -.03098993562162,
    -.04496069997549,
    .04446176066995,
    .01869462057948,
    -.20081178843975,
    -.08008606731892,
    -.08214038610458,
    -.38369914889336,
    -.03162068501115,
    -.24543529748917,
    -.22040157020092,
    -.20144037902355,
    -.18708138167858,
    -.07620526105165,
    .01427639275789,
    .48931872844696,
    -.11833623051643,
    .78889113664627,
    .43461054563522,
    .45327401161194,
    .27393117547035,
    .73159569501877,
    .21077930927277,
    -.40970605611801,
    -.01871551014483,
    -.10306061804295,
    -.0734596773982,
    -.65103828907013,
    .0724478662014,
    .05945380032063,
    -.05038867890835,
    .45991089940071,
    -.12104434520006,
    -.09359546005726,
    .22719417512417,
    .28968048095703,
    .64352011680603,
    .53756183385849,
    .25732442736626,
    .93205803632736,
    1.0886732339859,
    .72682982683182,
    1.2397809028625,
    1.1673469543457,
    -.18098846077919,
    .31969723105431,
    .72494095563889,
    .05790812522173,
    .61364978551865,
    .06710703670979,
    -.77938556671143,
    -.97910648584366,
    1.1435683965683,
    -.92507529258728,
    -1.5155116319656,
    -.11481033265591,
    .01764474436641,
    .02447287365794,
    .32963913679123,
    .18031190335751,
    -.23930950462818,
    .01684862375259,
    -.37613153457642,
    .40019443631172,
    -.2662724852562,
    -.11008904129267,
    .51469951868057,
    -2.1730391979218,
    .22205695509911,
    .06622361391783,
    .54170626401901,
    .53436845541,
    .2353515625,
    .29585054516792,
    .04819770529866,
    .24760706722736,
    .31166675686836,
    .36669155955315,
    .21487690508366,
    .79340130090714,
    .17062658071518,
    -.33359375596046,
    .95196217298508,
    .10373862832785,
    .31576481461525,
    1.589346408844,
    -.26140204071999,
    -1.0672763586044,
    -.13626158237457,
    -.18554861843586,
    -.02939598634839,
    -.00464448658749,
    .01412893645465,
    .1283223181963,
    .02133745700121,
    -.06621573865414,
    -.33903631567955,
    .13481116294861,
    -.28028702735901,
    -.02071117423475,
    .28890857100487,
    .04294065013528,
    .14363515377045,
    .32640132308006,
    -.22214868664742,
    -.0701690018177,
    .25145494937897,
    .41458681225777,
    -.14886146783829,
    .19186246395111,
    .16944620013237,
    -.54752624034882,
    -.43612506985664,
    .2482432872057,
    -.39438369870186,
    -.62015581130981,
    .28931456804276,
    -.06979911774397,
    .03869699314237,
    .4273681640625,
    -.05220314115286,
    .55854320526123,
    .26015737652779,
    .62115871906281,
    .72063958644867,
    .00899865385145,
    .53098171949387,
    -.44116449356079,
    -.13600566983223,
    -.89187180995941,
    -.81647485494614,
    .83413660526276,
    -.21809615194798,
    .32638800144196,
    .47133237123489,
    -.4058920443058,
    -.42233863472939,
    .35867437720299,
    .49578228592873,
    .11262346804142,
    .70294010639191,
    .58906590938568,
    -.19715182483196,
    .86181098222733,
    -.37105345726013,
    3.3236031532288,
    -1.543759226799,
    -.11011194437742,
    .64728397130966,
    -2.2335081100464,
    .67635416984558,
    1.2392344474792,
    .10933646559715,
    .49816474318504,
    2.0072033405304,
    -.17484994232655,
    3.0224411487579,
    -3.7984521389008,
    -6.0368394851685,
    .27887633442879,
    1.4904805421829,
    1.3098726272583])

yr = np.array([
    np.nan,
    -.6596063375473,
    -.49726036190987,
    -.5911386013031,
    -.34607490897179,
    -.46362805366516,
    -.21342028677464,
    -.31237986683846,
    -.40454092621803,
    -.22221945226192,
    -.26514956355095,
    -.2509354352951,
    -.13019436597824,
    -.30159646272659,
    -.1318296790123,
    -.24349159002304,
    -.25457563996315,
    -.07427024841309,
    -.24664734303951,
    -.10696394741535,
    -.30270880460739,
    -.22268049418926,
    -.18816292285919,
    -.13006833195686,
    -.20216277241707,
    -.10066751390696,
    -.24728938937187,
    -.07679972797632,
    .07274255156517,
    -.20292413234711,
    .03331403434277,
    -.35277983546257,
    -.16799576580524,
    -.06826904416084,
    -.08623649924994,
    .00015908146452,
    -.12788754701614,
    .06861615926027,
    -.06953293830156,
    -.08066567778587,
    .11089706420898,
    -.03098993562162,
    -.04496069997549,
    .04446176066995,
    .01869462057948,
    -.20081178843975,
    -.08008606731892,
    -.08214038610458,
    -.38369914889336,
    -.03162068501115,
    -.24543529748917,
    -.22040157020092,
    -.20144037902355,
    -.18708138167858,
    -.07620526105165,
    .01427639275789,
    .48931872844696,
    -.11833623051643,
    .78889113664627,
    .43461054563522,
    .45327401161194,
    .27393117547035,
    .73159569501877,
    .21077930927277,
    -.40970605611801,
    -.01871551014483,
    -.10306061804295,
    -.0734596773982,
    -.65103828907013,
    .0724478662014,
    .05945380032063,
    -.05038867890835,
    .45991089940071,
    -.12104434520006,
    -.09359546005726,
    .22719417512417,
    .28968048095703,
    .64352011680603,
    .53756183385849,
    .25732442736626,
    .93205803632736,
    1.0886732339859,
    .72682982683182,
    1.2397809028625,
    1.1673469543457,
    -.18098846077919,
    .31969723105431,
    .72494095563889,
    .05790812522173,
    .61364978551865,
    .06710703670979,
    -.77938556671143,
    -.97910648584366,
    1.1435683965683,
    -.92507529258728,
    -1.5155116319656,
    -.11481033265591,
    .01764474436641,
    .02447287365794,
    .32963913679123,
    .18031190335751,
    -.23930950462818,
    .01684862375259,
    -.37613153457642,
    .40019443631172,
    -.2662724852562,
    -.11008904129267,
    .51469951868057,
    -2.1730391979218,
    .22205695509911,
    .06622361391783,
    .54170626401901,
    .53436845541,
    .2353515625,
    .29585054516792,
    .04819770529866,
    .24760706722736,
    .31166675686836,
    .36669155955315,
    .21487690508366,
    .79340130090714,
    .17062658071518,
    -.33359375596046,
    .95196217298508,
    .10373862832785,
    .31576481461525,
    1.589346408844,
    -.26140204071999,
    -1.0672763586044,
    -.13626158237457,
    -.18554861843586,
    -.02939598634839,
    -.00464448658749,
    .01412893645465,
    .1283223181963,
    .02133745700121,
    -.06621573865414,
    -.33903631567955,
    .13481116294861,
    -.28028702735901,
    -.02071117423475,
    .28890857100487,
    .04294065013528,
    .14363515377045,
    .32640132308006,
    -.22214868664742,
    -.0701690018177,
    .25145494937897,
    .41458681225777,
    -.14886146783829,
    .19186246395111,
    .16944620013237,
    -.54752624034882,
    -.43612506985664,
    .2482432872057,
    -.39438369870186,
    -.62015581130981,
    .28931456804276,
    -.06979911774397,
    .03869699314237,
    .4273681640625,
    -.05220314115286,
    .55854320526123,
    .26015737652779,
    .62115871906281,
    .72063958644867,
    .00899865385145,
    .53098171949387,
    -.44116449356079,
    -.13600566983223,
    -.89187180995941,
    -.81647485494614,
    .83413660526276,
    -.21809615194798,
    .32638800144196,
    .47133237123489,
    -.4058920443058,
    -.42233863472939,
    .35867437720299,
    .49578228592873,
    .11262346804142,
    .70294010639191,
    .58906590938568,
    -.19715182483196,
    .86181098222733,
    -.37105345726013,
    3.3236031532288,
    -1.543759226799,
    -.11011194437742,
    .64728397130966,
    -2.2335081100464,
    .67635416984558,
    1.2392344474792,
    .10933646559715,
    .49816474318504,
    2.0072033405304,
    -.17484994232655,
    3.0224411487579,
    -3.7984521389008,
    -6.0368394851685,
    .27887633442879,
    1.4904805421829,
    1.3098726272583])

mse = np.array([
    1.0121052265167,
    .66349595785141,
    .65449619293213,
    .64957880973816,
    .64683443307877,
    .64528465270996,
    .64440369606018,
    .64390099048615,
    .64361357688904,
    .64344894886017,
    .64335465431213,
    .64330065250397,
    .64326965808868,
    .64325189590454,
    .64324170351028,
    .6432358622551,
    .64323252439499,
    .64323055744171,
    .64322948455811,
    .64322882890701,
    .64322847127914,
    .64322829246521,
    .64322817325592,
    .64322811365128,
    .64322805404663,
    .64322805404663,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199,
    .64322799444199])

stdp = np.array([
    .82960641384125,
    .82960641384125,
    .697261095047,
    .61113905906677,
    .51607495546341,
    .47362637519836,
    .41342103481293,
    .40238001942635,
    .37454023957253,
    .33222004771233,
    .32514902949333,
    .31093680858612,
    .30019253492355,
    .31159669160843,
    .29182952642441,
    .30349296331406,
    .29457464814186,
    .28427124023438,
    .30664679408073,
    .29696446657181,
    .31270903348923,
    .29268020391464,
    .28816330432892,
    .29006817936897,
    .30216124653816,
    .30066826939583,
    .31728908419609,
    .30679926276207,
    .3272570669651,
    .37292611598969,
    .36668366193771,
    .40278288722038,
    .36799272894859,
    .36827209591866,
    .38623574376106,
    .39983862638474,
    .42789059877396,
    .43138384819031,
    .46953064203262,
    .48066720366478,
    .48910140991211,
    .53098994493484,
    .54496067762375,
    .55554050207138,
    .58130383491516,
    .60081332921982,
    .58008605241776,
    .58214038610458,
    .58369606733322,
    .53162068128586,
    .54543834924698,
    .52040082216263,
    .50143963098526,
    .48708060383797,
    .47620677947998,
    .48572361469269,
    .51068127155304,
    .61833620071411,
    .61110657453537,
    .76539021730423,
    .84672522544861,
    .92606955766678,
    .96840506792068,
    1.0892199277878,
    1.1097067594528,
    1.0187155008316,
    1.0030621290207,
    .97345739603043,
    .95103752613068,
    .82755368947983,
    .84054774045944,
    .85038793087006,
    .84008830785751,
    .92104357481003,
    .89359468221664,
    .87280809879303,
    .91032028198242,
    .95647835731506,
    1.0624366998672,
    1.1426770687103,
    1.1679404973984,
    1.311328291893,
    1.473167181015,
    1.5602221488953,
    1.7326545715332,
    1.8809853792191,
    1.7803012132645,
    1.7750589847565,
    1.8420933485031,
    1.7863517999649,
    1.8328944444656,
    1.7793855667114,
    1.5791050195694,
    1.3564316034317,
    1.5250737667084,
    1.3155146837234,
    1.014811873436,
    .98235523700714,
    .97552710771561,
    .97035628557205,
    1.0196926593781,
    1.0393049716949,
    .98315137624741,
    .97613000869751,
    .89980864524841,
    .96626943349838,
    .91009211540222,
    .88530200719833,
    .97303456068039,
    .57794612646103,
    .63377332687378,
    .65829831361771,
    .76562696695328,
    .86465454101563,
    .90414637327194,
    .95180231332779,
    .95238989591599,
    .98833626508713,
    1.0333099365234,
    1.0851185321808,
    1.1066001653671,
    1.2293750047684,
    1.233595252037,
    1.1480363607407,
    1.2962552309036,
    1.2842413187027,
    1.3106474876404,
    1.5614050626755,
    1.4672855138779,
    1.2362524271011,
    1.1855486631393,
    1.1294020414352,
    1.1046353578568,
    1.0858771800995,
    1.0716745853424,
    1.0786685943604,
    1.0662157535553,
    1.0390332937241,
    .96519494056702,
    .9802839756012,
    .92070508003235,
    .91108840703964,
    .95705932378769,
    .95637094974518,
    .97360169887543,
    1.0221517086029,
    .9701629281044,
    .94854199886322,
    .98542231321335,
    1.048855304718,
    1.0081344842911,
    1.0305507183075,
    1.0475262403488,
    .93612504005432,
    .85176283121109,
    .89438372850418,
    .820152759552,
    .71068543195724,
    .76979607343674,
    .76130604743958,
    .77262878417969,
    .85220617055893,
    .84146595001221,
    .93983960151672,
    .97883212566376,
    1.0793634653091,
    1.1909983158112,
    1.1690304279327,
    1.2411522865295,
    1.1360056400299,
    1.0918840169907,
    .9164656996727,
    .76586949825287,
    .918093085289,
    .87360894680023,
    .92867678403854,
    1.00588285923,
    .92233866453171,
    .84132260084152,
    .90422683954239,
    .9873673915863,
    .99707210063934,
    1.1109310388565,
    1.1971517801285,
    1.138188958168,
    1.2710473537445,
    1.1763968467712,
    1.7437561750412,
    1.4101150035858,
    1.3527159690857,
    1.4335050582886,
    .99765706062317,
    1.1067585945129,
    1.3086627721786,
    1.2968333959579,
    1.3547962903976,
    1.6768488883972,
    1.5905654430389,
    2.0774590969086,
    1.3218278884888,
    .21813294291496,
    .30750840902328,
    .60612773895264])

icstats = np.array([
    202,
    np.nan,
    -242.06033399744,
    4,
    492.12066799488,
    505.35373878448])


results = Bunch(
    llf=llf,
    nobs=nobs,
    k=k,
    k_exog=k_exog,
    sigma=sigma,
    chi2=chi2,
    df_model=df_model,
    k_ar=k_ar,
    k_ma=k_ma,
    params=params,
    cov_params=cov_params,
    xb=xb,
    y=y,
    resid=resid,
    yr=yr,
    mse=mse,
    stdp=stdp,
    icstats=icstats
)
