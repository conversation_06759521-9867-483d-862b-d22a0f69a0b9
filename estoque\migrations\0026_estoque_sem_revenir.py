# Generated by Django 5.2 on 2025-06-25 13:41

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('estoque', '0025_previsaodemanda_grupo_sessao_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='EstoqueSemRevenir',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantidade', models.IntegerField(default=0, help_text='Quantidade em estoque sem revenir')),
                ('data_ultima_movimentacao', models.DateTimeField(auto_now=True)),
                ('observacoes', models.TextField(blank=True, help_text='Observações sobre o estoque sem revenir', null=True)),
                ('mola', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='estoque_sem_revenir', to='estoque.mola')),
            ],
            options={
                'verbose_name': 'Estoque Sem Revenir',
                'verbose_name_plural': 'Estoques Sem Revenir',
            },
        ),
        migrations.CreateModel(
            name='MovimentacaoEstoqueSemRevenir',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('tipo', models.CharField(choices=[('E', 'Entrada'), ('S', 'Saída'), ('T', 'Transferência para estoque normal')], max_length=1)),
                ('quantidade', models.IntegerField()),
                ('observacao', models.TextField(blank=True, null=True)),
                ('data', models.DateTimeField(auto_now_add=True)),
                ('estoque_sem_revenir', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='movimentacoes', to='estoque.estoquesemrevenir')),
                ('ordem_fabricacao', models.ForeignKey(blank=True, help_text='Ordem de fabricação relacionada (se aplicável)', null=True, on_delete=django.db.models.deletion.SET_NULL, to='estoque.planejamentoproducao')),
            ],
            options={
                'verbose_name': 'Movimentação Estoque Sem Revenir',
                'verbose_name_plural': 'Movimentações Estoque Sem Revenir',
                'ordering': ['-data'],
            },
        ),
        migrations.AddConstraint(
            model_name='estoquesemrevenir',
            constraint=models.UniqueConstraint(fields=('mola',), name='unique_mola_estoque_sem_revenir'),
        ),
    ]
