{% extends 'estoque/base.html' %}
{% load static %}

{% block title %}Dashboard Executivo - Controle de Estoque{% endblock %}

{% block extra_css %}
<style>
    .kpi-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }
    
    .kpi-card.success {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }
    
    .kpi-card.warning {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    }
    
    .kpi-card.danger {
        background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    }
    
    .kpi-value {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 5px;
    }
    
    .kpi-label {
        font-size: 0.9rem;
        opacity: 0.9;
    }
    
    .kpi-trend {
        font-size: 0.8rem;
        margin-top: 10px;
    }
    
    .trend-up {
        color: #28a745;
    }
    
    .trend-down {
        color: #dc3545;
    }
    
    .alert-card {
        border-left: 4px solid;
        padding: 15px;
        margin-bottom: 15px;
        border-radius: 5px;
    }
    
    .alert-warning {
        border-left-color: #ffc107;
        background-color: #fff3cd;
    }
    
    .alert-danger {
        border-left-color: #dc3545;
        background-color: #f8d7da;
    }
    
    .turnover-high {
        color: #28a745;
        font-weight: bold;
    }
    
    .turnover-medium {
        color: #ffc107;
        font-weight: bold;
    }
    
    .turnover-low {
        color: #dc3545;
        font-weight: bold;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Cabeçalho -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-chart-line"></i> Dashboard Executivo
        </h1>
        <div class="text-muted">
            <small>Última atualização: {{ timestamp|date:"d/m/Y H:i" }}</small>
        </div>
    </div>

    <!-- KPIs Principais -->
    <div class="row">
        <!-- Vendas Mês Atual -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="kpi-card">
                <div class="kpi-value">{{ vendas_mes_atual|default:0 }}</div>
                <div class="kpi-label">Vendas Mês Atual</div>
                <div class="kpi-trend">
                    {% if crescimento_mom > 0 %}
                        <i class="fas fa-arrow-up trend-up"></i> +{{ crescimento_mom }}%
                    {% elif crescimento_mom < 0 %}
                        <i class="fas fa-arrow-down trend-down"></i> {{ crescimento_mom }}%
                    {% else %}
                        <i class="fas fa-minus text-muted"></i> 0%
                    {% endif %}
                    vs. mês anterior
                </div>
            </div>
        </div>

        <!-- Vendas Mês Anterior -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="kpi-card success">
                <div class="kpi-value">{{ vendas_mes_anterior|default:0 }}</div>
                <div class="kpi-label">Vendas Mês Anterior</div>
                <div class="kpi-trend">
                    <i class="fas fa-calendar-alt"></i> Referência
                </div>
            </div>
        </div>

        <!-- Crescimento MoM -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="kpi-card {% if crescimento_mom > 0 %}success{% elif crescimento_mom < -10 %}danger{% else %}warning{% endif %}">
                <div class="kpi-value">{{ crescimento_mom }}%</div>
                <div class="kpi-label">Crescimento MoM</div>
                <div class="kpi-trend">
                    <i class="fas fa-chart-line"></i> Month over Month
                </div>
            </div>
        </div>

        <!-- Total de Alertas -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="kpi-card {% if alertas|length > 3 %}danger{% elif alertas|length > 1 %}warning{% else %}success{% endif %}">
                <div class="kpi-value">{{ alertas|length }}</div>
                <div class="kpi-label">Alertas Ativos</div>
                <div class="kpi-trend">
                    <i class="fas fa-exclamation-triangle"></i> Requer atenção
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Top Molas com KPIs Avançados -->
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-trophy"></i> Top 5 Molas - KPIs Avançados
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>Código</th>
                                    <th>Cliente</th>
                                    <th>Vendas</th>
                                    <th>Inventory Turnover</th>
                                    <th>DSO (dias)</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for mola in top_molas %}
                                <tr>
                                    <td><strong>{{ mola.codigo }}</strong></td>
                                    <td>{{ mola.cliente }}</td>
                                    <td>{{ mola.vendas }}</td>
                                    <td>
                                        <span class="turnover-{{ mola.status_turnover }}">
                                            {{ mola.inventory_turnover }}x
                                        </span>
                                    </td>
                                    <td>{{ mola.dso }} dias</td>
                                    <td>
                                        {% if mola.status_turnover == 'high' %}
                                            <span class="badge badge-success">Excelente</span>
                                        {% elif mola.status_turnover == 'medium' %}
                                            <span class="badge badge-warning">Bom</span>
                                        {% else %}
                                            <span class="badge badge-danger">Baixo</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="6" class="text-center text-muted">
                                        Nenhuma venda registrada no período
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Explicação dos KPIs -->
                    <div class="mt-3">
                        <small class="text-muted">
                            <strong>Inventory Turnover:</strong> Quantas vezes o estoque gira por ano (>6 = Excelente, 2-6 = Bom, <2 = Baixo)<br>
                            <strong>DSO:</strong> Days Sales Outstanding - Quantos dias de vendas o estoque atual representa
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Alertas Inteligentes -->
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-bell"></i> Alertas Inteligentes
                    </h6>
                </div>
                <div class="card-body">
                    {% if alertas %}
                        {% for alerta in alertas %}
                        <div class="alert-card alert-{{ alerta.tipo }}">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h6 class="mb-1">{{ alerta.titulo }}</h6>
                                    <p class="mb-1">{{ alerta.mensagem }}</p>
                                    <small class="text-muted">{{ alerta.acao }}</small>
                                </div>
                                <div>
                                    {% if alerta.tipo == 'warning' %}
                                        <i class="fas fa-exclamation-triangle text-warning"></i>
                                    {% elif alerta.tipo == 'danger' %}
                                        <i class="fas fa-exclamation-circle text-danger"></i>
                                    {% else %}
                                        <i class="fas fa-info-circle text-info"></i>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center text-success">
                            <i class="fas fa-check-circle fa-3x mb-3"></i>
                            <p>Nenhum alerta ativo!</p>
                            <small class="text-muted">Todos os indicadores estão dentro dos parâmetros normais.</small>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Ações Rápidas -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-bolt"></i> Ações Rápidas
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <a href="{% url 'relatorio-molas-mais-vendidas' %}" class="btn btn-primary btn-block">
                                <i class="fas fa-chart-bar"></i> Relatório Vendas
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{% url 'relatorio-estoque' %}" class="btn btn-info btn-block">
                                <i class="fas fa-boxes"></i> Relatório Estoque
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{% url 'analise-obsolescencia' %}" class="btn btn-warning btn-block">
                                <i class="fas fa-exclamation-triangle"></i> Análise Obsolescência
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{% url 'dashboard-kpi' %}" class="btn btn-success btn-block">
                                <i class="fas fa-tachometer-alt"></i> Dashboard KPI
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Auto-refresh a cada 5 minutos
    setTimeout(function() {
        location.reload();
    }, 300000);
</script>
{% endblock %}
