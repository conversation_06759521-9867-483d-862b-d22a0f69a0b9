/**
 * Utilitários JavaScript para o sistema de controle de estoque
 */

/**
 * Extrai o valor numérico de uma string de diâmetro
 * @param {string} diameterStr - String contendo o diâmetro (ex: "0,10 mm", "Ø 0.40 mm")
 * @returns {number} - Valor numérico do diâmetro
 */
function extractNumericValue(diameterStr) {
    if (!diameterStr) return 0;
    
    // Substituir vírgulas por pontos para garantir formato decimal correto
    diameterStr = diameterStr.replace(',', '.');
    
    // Remover caracteres não numéricos e manter apenas números e pontos
    const numericStr = diameterStr.replace(/[^\d.]/g, '');
    
    // Converter para número
    const numericValue = parseFloat(numericStr);
    
    // Retornar 0 se não for um número válido
    return isNaN(numericValue) ? 0 : numericValue;
}

/**
 * Ordena materiais por nome e diâmetro numérico
 * @param {Array} materiais - Array de objetos material com propriedades 'nome' e 'diametro'
 * @returns {Array} - Array ordenado de materiais
 */
function ordenarMateriais(materiais) {
    if (!materiais || !Array.isArray(materiais)) return [];
    
    return materiais.sort((a, b) => {
        // Primeiro ordenar por nome
        const nomeA = a.nome || '';
        const nomeB = b.nome || '';
        
        // Se os nomes são diferentes, ordenar por nome
        if (nomeA !== nomeB) {
            return nomeA.localeCompare(nomeB);
        }
        
        // Se os nomes são iguais, ordenar por diâmetro numérico
        const diametroA = extractNumericValue(a.diametro || '');
        const diametroB = extractNumericValue(b.diametro || '');
        
        return diametroA - diametroB;
    });
}

/**
 * Preenche um elemento select com opções de materiais ordenados
 * @param {HTMLSelectElement} selectElement - Elemento select a ser preenchido
 * @param {Array} materiais - Array de objetos material com propriedades 'id', 'nome' e 'diametro'
 * @param {number} selectedId - ID do material a ser selecionado (opcional)
 */
function preencherSelectMateriais(selectElement, materiais, selectedId = null) {
    if (!selectElement || !materiais) return;
    
    // Limpar opções existentes
    selectElement.innerHTML = '';
    
    // Adicionar opção vazia
    const emptyOption = document.createElement('option');
    emptyOption.value = '';
    emptyOption.textContent = 'Selecione um material';
    selectElement.appendChild(emptyOption);
    
    // Ordenar materiais
    const materiaisOrdenados = ordenarMateriais(materiais);
    
    // Adicionar opções ordenadas
    materiaisOrdenados.forEach(material => {
        const option = document.createElement('option');
        option.value = material.id;
        option.textContent = material.nome;
        
        // Selecionar se for o material escolhido
        if (selectedId && material.id == selectedId) {
            option.selected = true;
        }
        
        selectElement.appendChild(option);
    });
}
