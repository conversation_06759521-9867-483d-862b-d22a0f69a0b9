{% extends 'estoque/base.html' %}

{% block title %}Confirmar Exclusão de Item - Molas Rios{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Confirmar Exclusão de Item</h1>
        <a href="{% url 'pedido-venda-detail' pedido.id %}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Voltar
        </a>
    </div>

    <div class="card shadow">
        <div class="card-body">
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i> Você está prestes a remover um item do pedido. Esta ação não pode ser desfeita.
            </div>

            <h4>Detalhes do Item</h4>
            <div class="table-responsive">
                <table class="table table-bordered">
                    <tr>
                        <th style="width: 30%">Pedido:</th>
                        <td>#{{ pedido.numero_pedido }}</td>
                    </tr>
                    <tr>
                        <th>Mola:</th>
                        <td>{{ item.mola.codigo }}</td>
                    </tr>
                    <tr>
                        <th>Cliente:</th>
                        <td>{{ item.mola.cliente }}</td>
                    </tr>
                    <tr>
                        <th>Quantidade:</th>
                        <td>{{ item.quantidade }}</td>
                    </tr>
                    <tr>
                        <th>Status:</th>
                        <td>
                            {% if item.atendido %}
                                <span class="badge bg-success">Atendido</span>
                            {% else %}
                                <span class="badge bg-warning">Pendente</span>
                            {% endif %}
                        </td>
                    </tr>
                </table>
            </div>
            
            <div class="mt-4">
                <form method="post">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash"></i> Confirmar Exclusão
                    </button>
                    <a href="{% url 'pedido-venda-detail' pedido.id %}" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Cancelar
                    </a>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
