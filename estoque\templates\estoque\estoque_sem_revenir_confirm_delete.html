{% extends 'estoque/base.html' %}

{% block title %}Confirmar Exclusão - Estoque Sem Revenir{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Confirmar Exclusão</h1>
        <a href="{% url 'estoque-sem-revenir-list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Voltar
        </a>
    </div>

    <div class="card shadow">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-danger">
                <i class="fas fa-exclamation-triangle"></i> Confirmar Exclusão
            </h6>
        </div>
        <div class="card-body">
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle"></i>
                <strong>Atenção!</strong> Esta ação não pode ser desfeita.
            </div>

            <p>Tem certeza de que deseja remover a seguinte mola do estoque sem revenir?</p>

            <div class="card bg-light">
                <div class="card-body">
                    <h5 class="card-title">{{ estoque.mola.codigo }}</h5>
                    <p class="card-text">
                        <strong>Cliente:</strong> {{ estoque.mola.cliente }}<br>
                        <strong>Quantidade em estoque:</strong> {{ estoque.quantidade }}<br>
                        {% if estoque.mola.material_padrao %}
                            <strong>Material:</strong> {{ estoque.mola.material_padrao.nome }}<br>
                        {% elif estoque.mola.material %}
                            <strong>Material:</strong> {{ estoque.mola.material.nome }}<br>
                        {% endif %}
                        {% if estoque.observacoes %}
                            <strong>Observações:</strong> {{ estoque.observacoes }}<br>
                        {% endif %}
                        <strong>Última movimentação:</strong> {{ estoque.data_ultima_movimentacao|date:"d/m/Y H:i" }}
                    </p>
                </div>
            </div>

            <form method="post" class="mt-4">
                {% csrf_token %}
                <button type="submit" class="btn btn-danger">
                    <i class="fas fa-trash"></i> Sim, remover do estoque
                </button>
                <a href="{% url 'estoque-sem-revenir-list' %}" class="btn btn-secondary">
                    <i class="fas fa-times"></i> Cancelar
                </a>
            </form>
        </div>
    </div>
</div>
{% endblock %}
