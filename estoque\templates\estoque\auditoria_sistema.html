{% extends 'estoque/base.html' %}
{% load static %}

{% block title %}Auditoria do Sistema{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Auditoria Completa do Sistema</h1>
    </div>

    {% if not executada %}
    <div class="row">
        <div class="col-lg-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Executar Auditoria</h6>
                </div>
                <div class="card-body">
                    <p>A auditoria completa do sistema irá verificar:</p>
                    <ul>
                        <li>Consistência dos dados</li>
                        <li>Integridade referencial</li>
                        <li>Precisão dos cálculos</li>
                        <li>Funcionalidades redundantes</li>
                        <li>Funcionalidades em falta</li>
                        <li>Estatísticas gerais</li>
                    </ul>
                    
                    <form method="post">
                        {% csrf_token %}
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i> Executar Auditoria
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
    {% else %}
    
    <!-- Estatísticas Gerais -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total de Molas
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ resultados.estatisticas_gerais.total_molas }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-cogs fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Total de Movimentações
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ resultados.estatisticas_gerais.total_movimentacoes }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exchange-alt fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Total de Pedidos
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ resultados.estatisticas_gerais.total_pedidos }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Valor Total Estoque
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                R$ {{ resultados.estatisticas_gerais.valor_total_estoque|floatformat:2 }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Problemas Críticos -->
    {% if resultados.problemas_criticos %}
    <div class="row">
        <div class="col-lg-12">
            <div class="card shadow mb-4 border-left-danger">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-danger">
                        <i class="fas fa-exclamation-triangle"></i> Problemas Críticos ({{ resultados.problemas_criticos|length }})
                    </h6>
                </div>
                <div class="card-body">
                    {% for problema in resultados.problemas_criticos %}
                    <div class="alert alert-danger">
                        <h6><strong>{{ problema.tipo }}</strong></h6>
                        <p>{{ problema.descricao }}</p>
                        <p><strong>Ação recomendada:</strong> {{ problema.acao }}</p>
                        {% if problema.detalhes %}
                        <details>
                            <summary>Ver detalhes</summary>
                            <ul>
                                {% for detalhe in problema.detalhes %}
                                <li>{{ detalhe }}</li>
                                {% endfor %}
                            </ul>
                        </details>
                        {% endif %}
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Problemas Médios -->
    {% if resultados.problemas_medios %}
    <div class="row">
        <div class="col-lg-12">
            <div class="card shadow mb-4 border-left-warning">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-warning">
                        <i class="fas fa-exclamation-circle"></i> Problemas Médios ({{ resultados.problemas_medios|length }})
                    </h6>
                </div>
                <div class="card-body">
                    {% for problema in resultados.problemas_medios %}
                    <div class="alert alert-warning">
                        <h6><strong>{{ problema.tipo }}</strong></h6>
                        <p>{{ problema.descricao }}</p>
                        <p><strong>Ação recomendada:</strong> {{ problema.acao }}</p>
                        {% if problema.detalhes %}
                        <details>
                            <summary>Ver detalhes</summary>
                            <ul>
                                {% for detalhe in problema.detalhes %}
                                <li>{{ detalhe }}</li>
                                {% endfor %}
                            </ul>
                        </details>
                        {% endif %}
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Problemas Menores -->
    {% if resultados.problemas_menores %}
    <div class="row">
        <div class="col-lg-12">
            <div class="card shadow mb-4 border-left-info">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="fas fa-info-circle"></i> Problemas Menores ({{ resultados.problemas_menores|length }})
                    </h6>
                </div>
                <div class="card-body">
                    {% for problema in resultados.problemas_menores %}
                    <div class="alert alert-info">
                        <h6><strong>{{ problema.tipo }}</strong></h6>
                        <p>{{ problema.descricao }}</p>
                        <p><strong>Ação recomendada:</strong> {{ problema.acao }}</p>
                        {% if problema.detalhes %}
                        <details>
                            <summary>Ver detalhes</summary>
                            <ul>
                                {% for detalhe in problema.detalhes %}
                                <li>{{ detalhe }}</li>
                                {% endfor %}
                            </ul>
                        </details>
                        {% endif %}
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Recomendações -->
    <div class="row">
        <div class="col-lg-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-lightbulb"></i> Recomendações ({{ resultados.recomendacoes|length }})
                    </h6>
                </div>
                <div class="card-body">
                    {% for recomendacao in resultados.recomendacoes %}
                    <div class="alert {% if recomendacao.prioridade == 'CRÍTICA' %}alert-danger{% elif recomendacao.prioridade == 'ALTA' %}alert-warning{% else %}alert-info{% endif %}">
                        <h6>
                            <span class="badge {% if recomendacao.prioridade == 'CRÍTICA' %}badge-danger{% elif recomendacao.prioridade == 'ALTA' %}badge-warning{% else %}badge-info{% endif %}">
                                {{ recomendacao.prioridade }}
                            </span>
                            {{ recomendacao.titulo }}
                        </h6>
                        <p>{{ recomendacao.descricao }}</p>
                        <p><strong>Ação:</strong> {{ recomendacao.acao }}</p>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>

    <!-- Botão para nova auditoria -->
    <div class="row">
        <div class="col-lg-12">
            <form method="post">
                {% csrf_token %}
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-redo"></i> Executar Nova Auditoria
                </button>
            </form>
        </div>
    </div>
    
    {% endif %}
</div>
{% endblock %}
