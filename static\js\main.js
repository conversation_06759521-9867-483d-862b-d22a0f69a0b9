/**
 * Script principal para o sistema de controle de estoque
 * Contém funções para melhorar a interatividade e usabilidade
 */

document.addEventListener('DOMContentLoaded', function() {
    // Inicializar tooltips
    initTooltips();

    // Adicionar comportamento de navegação por teclas
    setupKeyboardNavigation();

    // Configurar comportamento de tabelas
    setupTables();

    // Configurar comportamento de formulários
    setupForms();

    // Configurar comportamento de alertas
    setupAlerts();

    // Inicializar contadores
    initCounters();

    // Ordenar selects de materiais
    ordenarSelectsMateriais();

    console.log('Sistema de Controle de Estoque inicializado com sucesso.');
});

/**
 * Inicializa tooltips para elementos com atributo data-tooltip
 */
function initTooltips() {
    const tooltipElements = document.querySelectorAll('[data-tooltip]');

    tooltipElements.forEach(element => {
        const tooltipText = element.getAttribute('data-tooltip');

        // Criar elemento de tooltip
        const tooltip = document.createElement('span');
        tooltip.className = 'tooltip-text';
        tooltip.textContent = tooltipText;

        // Adicionar classe tooltip ao elemento pai
        element.classList.add('tooltip');

        // Adicionar tooltip ao elemento
        element.appendChild(tooltip);
    });
}

/**
 * Configura navegação por teclado
 */
function setupKeyboardNavigation() {
    // Navegação entre campos de formulário com Enter
    document.querySelectorAll('input, select, textarea').forEach(input => {
        input.addEventListener('keydown', function(e) {
            // Se pressionar Enter
            if (e.key === 'Enter') {
                e.preventDefault();

                // Encontrar o próximo campo
                const form = this.closest('form');
                if (!form) return;

                const inputs = Array.from(form.querySelectorAll('input, select, textarea:not([readonly])'));
                const currentIndex = inputs.indexOf(this);
                const nextInput = inputs[currentIndex + 1];

                // Focar no próximo campo ou submeter o formulário
                if (nextInput) {
                    nextInput.focus();
                } else {
                    // Se for o último campo, verificar se há um botão de submit
                    const submitButton = form.querySelector('button[type="submit"], input[type="submit"]');
                    if (submitButton) {
                        submitButton.click();
                    }
                }
            }
        });
    });

    // Atalhos de teclado globais
    document.addEventListener('keydown', function(e) {
        // Alt + N para novo item (depende da página)
        if (e.altKey && e.key === 'n') {
            e.preventDefault();
            const newButton = document.querySelector('.btn-new, .btn-add, a[href*="new"], a[href*="add"]');
            if (newButton) newButton.click();
        }

        // Alt + S para salvar
        if (e.altKey && e.key === 's') {
            e.preventDefault();
            const saveButton = document.querySelector('button[type="submit"], input[type="submit"]');
            if (saveButton) saveButton.click();
        }

        // Alt + B para voltar
        if (e.altKey && e.key === 'b') {
            e.preventDefault();
            const backButton = document.querySelector('.btn-back, a[href*="back"], a[href*="list"]');
            if (backButton) backButton.click();
        }
    });
}

/**
 * Configura comportamento de tabelas
 */
function setupTables() {
    // Tornar linhas de tabela clicáveis
    document.querySelectorAll('table.table-clickable tbody tr').forEach(row => {
        row.style.cursor = 'pointer';

        row.addEventListener('click', function() {
            const link = this.querySelector('a');
            if (link) {
                window.location.href = link.href;
            }
        });
    });

    // Ordenação de tabelas
    document.querySelectorAll('th.sortable').forEach(header => {
        header.style.cursor = 'pointer';

        header.addEventListener('click', function() {
            const table = this.closest('table');
            const index = Array.from(this.parentNode.children).indexOf(this);
            const rows = Array.from(table.querySelectorAll('tbody tr'));

            // Determinar direção da ordenação
            const currentDirection = this.getAttribute('data-sort-direction') || 'asc';
            const newDirection = currentDirection === 'asc' ? 'desc' : 'asc';

            // Atualizar atributo de direção
            this.setAttribute('data-sort-direction', newDirection);

            // Ordenar linhas
            rows.sort((a, b) => {
                const cellA = a.children[index].textContent.trim();
                const cellB = b.children[index].textContent.trim();

                // Verificar se é uma coluna de diâmetro
                const isDiameterColumn = this.textContent.toLowerCase().includes('diâmetro') ||
                                        this.textContent.toLowerCase().includes('diametro');

                if (isDiameterColumn) {
                    // Extrair valores numéricos do diâmetro (ignorando texto como "mm" ou "Ø")
                    const diameterA = extractNumericValue(cellA);
                    const diameterB = extractNumericValue(cellB);

                    return newDirection === 'asc' ? diameterA - diameterB : diameterB - diameterA;
                }

                // Tentar ordenar como número para outras colunas
                const numA = parseFloat(cellA);
                const numB = parseFloat(cellB);

                if (!isNaN(numA) && !isNaN(numB)) {
                    return newDirection === 'asc' ? numA - numB : numB - numA;
                }

                // Ordenar como texto
                return newDirection === 'asc'
                    ? cellA.localeCompare(cellB)
                    : cellB.localeCompare(cellA);
            });

            // Reordenar tabela
            const tbody = table.querySelector('tbody');
            rows.forEach(row => tbody.appendChild(row));
        });
    });
}

/**
 * Configura comportamento de formulários
 */
function setupForms() {
    // Limpar campos de formulário ao clicar em botão de limpar
    document.querySelectorAll('.btn-clear-form').forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();

            const form = this.closest('form');
            if (!form) return;

            // Limpar campos
            form.querySelectorAll('input:not([type="submit"]):not([type="button"]), select, textarea').forEach(field => {
                if (field.type === 'checkbox' || field.type === 'radio') {
                    field.checked = false;
                } else {
                    field.value = '';
                }
            });
        });
    });

    // Adicionar validação de formulários
    document.querySelectorAll('form.needs-validation').forEach(form => {
        form.addEventListener('submit', function(e) {
            if (!this.checkValidity()) {
                e.preventDefault();
                e.stopPropagation();
            }

            this.classList.add('was-validated');
        });
    });

    // Adicionar máscaras para campos numéricos
    document.querySelectorAll('input[type="number"]').forEach(input => {
        input.addEventListener('input', function() {
            // Remover caracteres não numéricos
            this.value = this.value.replace(/[^0-9.-]/g, '');
        });
    });
}

/**
 * Configura comportamento de alertas
 */
function setupAlerts() {
    // Remover imediatamente todos os alertas de sucesso (pop-ups verdes)
    document.querySelectorAll('.alert.alert-success').forEach(alert => {
        alert.remove();
    });

    // Função para adicionar botões de fechamento aos alertas
    function addCloseButtonsToAlerts() {
        // Selecionar todos os alertas que não têm a classe alert-dismissible
        document.querySelectorAll('.alert:not(.alert-dismissible)').forEach(alert => {
            // Verificar se é um alerta de sucesso - remover imediatamente
            if (alert.classList.contains('alert-success')) {
                alert.remove();
                return;
            }

            // Adicionar classe alert-dismissible
            alert.classList.add('alert-dismissible', 'fade', 'show');

            // Verificar se já tem um botão de fechamento
            if (!alert.querySelector('.close, .btn-close')) {
                // Criar botão de fechamento
                const closeButton = document.createElement('button');
                closeButton.type = 'button';
                closeButton.className = 'btn-close';
                closeButton.setAttribute('data-bs-dismiss', 'alert');
                closeButton.setAttribute('aria-label', 'Fechar');

                // Adicionar estilo inline para garantir visibilidade
                closeButton.style.position = 'absolute';
                closeButton.style.right = '15px';
                closeButton.style.top = '15px';
                closeButton.style.fontSize = '1.5rem';
                closeButton.style.fontWeight = 'bold';
                closeButton.style.lineHeight = '1';
                closeButton.style.opacity = '0.7';
                closeButton.style.backgroundColor = 'transparent';
                closeButton.style.width = '24px';
                closeButton.style.height = '24px';
                closeButton.style.display = 'flex';
                closeButton.style.alignItems = 'center';
                closeButton.style.justifyContent = 'center';
                closeButton.style.padding = '0';
                closeButton.style.border = 'none';
                closeButton.style.cursor = 'pointer';

                // Definir cor com base no tipo de alerta
                const alertType = alert.classList.contains('alert-warning') ? 'black' : 'white';
                closeButton.style.color = alertType;

                // Adicionar texto X ao botão
                closeButton.textContent = '×';

                // Adicionar botão ao alerta
                alert.appendChild(closeButton);
            }
        });

        // Configurar comportamento de fechamento para todos os botões de fechar
        document.querySelectorAll('.alert .close, .alert .btn-close').forEach(button => {
            // Adicionar evento de hover
            button.addEventListener('mouseenter', function() {
                this.style.opacity = '1';
                this.style.backgroundColor = 'rgba(255, 255, 255, 0.1)';
            });

            button.addEventListener('mouseleave', function() {
                this.style.opacity = '0.7';
                this.style.backgroundColor = 'transparent';
            });

            // Adicionar evento de clique
            button.addEventListener('click', function() {
                const alert = this.closest('.alert');
                if (alert) {
                    alert.style.opacity = '0';
                    setTimeout(() => {
                        alert.style.display = 'none';
                    }, 300);
                }
            });
        });
    }

    // Executar imediatamente
    addCloseButtonsToAlerts();

    // Configurar um MutationObserver para monitorar mudanças no DOM
    // e adicionar botões de fechamento a novos alertas
    const observer = new MutationObserver(function(mutations) {
        let shouldAddButtons = false;

        mutations.forEach(function(mutation) {
            if (mutation.addedNodes.length) {
                for (let i = 0; i < mutation.addedNodes.length; i++) {
                    const node = mutation.addedNodes[i];
                    if (node.nodeType === 1) { // Elemento
                        // Verificar se o nó adicionado é um alerta de sucesso - remover imediatamente
                        if (node.classList && node.classList.contains('alert') && node.classList.contains('alert-success')) {
                            node.remove();
                        }
                        // Verificar se é um alerta de outro tipo
                        else if (node.classList && node.classList.contains('alert')) {
                            shouldAddButtons = true;
                        }
                        // Verificar se contém alertas
                        else if (node.querySelectorAll) {
                            // Remover alertas de sucesso imediatamente
                            const successAlerts = node.querySelectorAll('.alert.alert-success');
                            if (successAlerts.length > 0) {
                                successAlerts.forEach(alert => {
                                    alert.remove();
                                });
                            }

                            // Verificar outros alertas
                            const otherAlerts = node.querySelectorAll('.alert:not(.alert-success)');
                            if (otherAlerts.length > 0) {
                                shouldAddButtons = true;
                            }
                        }
                    }
                }
            }
        });

        if (shouldAddButtons) {
            addCloseButtonsToAlerts();
        }
    });

    // Observar todo o documento para mudanças no DOM
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
}

/**
 * Inicializa contadores animados
 */
function initCounters() {
    document.querySelectorAll('.counter').forEach(counter => {
        const target = parseInt(counter.getAttribute('data-target'));
        const duration = parseInt(counter.getAttribute('data-duration') || '1000');
        const increment = target / (duration / 16);
        let current = 0;

        const updateCounter = () => {
            current += increment;
            if (current >= target) {
                counter.textContent = target;
            } else {
                counter.textContent = Math.floor(current);
                requestAnimationFrame(updateCounter);
            }
        };

        updateCounter();
    });
}

/**
 * Função para confirmar ações destrutivas
 * @param {string} message - Mensagem de confirmação
 * @returns {boolean} - Verdadeiro se confirmado, falso caso contrário
 */
function confirmAction(message) {
    return confirm(message || 'Tem certeza que deseja realizar esta ação?');
}

/**
 * Função para formatar números como moeda
 * @param {number} value - Valor a ser formatado
 * @returns {string} - Valor formatado como moeda
 */
function formatCurrency(value) {
    return new Intl.NumberFormat('pt-BR', {
        style: 'currency',
        currency: 'BRL'
    }).format(value);
}

/**
 * Função para formatar datas
 * @param {string} dateString - Data em formato string
 * @returns {string} - Data formatada
 */
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('pt-BR');
}

/**
 * Função para formatar números
 * @param {number} value - Valor a ser formatado
 * @param {number} decimals - Número de casas decimais
 * @returns {string} - Valor formatado
 */
function formatNumber(value, decimals = 2) {
    return new Intl.NumberFormat('pt-BR', {
        minimumFractionDigits: decimals,
        maximumFractionDigits: decimals
    }).format(value);
}

/**
 * Extrai o valor numérico de uma string de diâmetro
 * @param {string} diameterStr - String contendo o diâmetro (ex: "0,10 mm", "Ø 0.40 mm")
 * @returns {number} - Valor numérico do diâmetro
 */
function extractNumericValue(diameterStr) {
    if (!diameterStr) return 0;

    // Substituir vírgulas por pontos para garantir formato decimal correto
    diameterStr = diameterStr.replace(',', '.');

    // Remover caracteres não numéricos e manter apenas números e pontos
    const numericStr = diameterStr.replace(/[^\d.]/g, '');

    // Converter para número
    const numericValue = parseFloat(numericStr);

    // Retornar 0 se não for um número válido
    return isNaN(numericValue) ? 0 : numericValue;
}

/**
 * Ordena todos os selects de materiais na página
 */
function ordenarSelectsMateriais() {
    // Selecionar todos os selects que contêm materiais
    const materialSelects = document.querySelectorAll('select[id*="material"]');

    materialSelects.forEach(function(select) {
        // Verificar se o select tem opções
        if (select.options.length <= 1) return;

        // Obter todas as opções (exceto a primeira que geralmente é "Selecione um material")
        const options = Array.from(select.options).slice(1);

        // Adicionar atributos data-nome e data-diametro se não existirem
        options.forEach(function(option) {
            if (!option.getAttribute('data-nome') || !option.getAttribute('data-diametro')) {
                const parts = (option.textContent || '').split(' - ');
                if (parts.length > 1) {
                    option.setAttribute('data-nome', parts[0]);
                    option.setAttribute('data-diametro', parts[1]);
                } else {
                    option.setAttribute('data-nome', option.textContent);
                    option.setAttribute('data-diametro', '');
                }
            }
        });

        // Ordenar as opções
        options.sort((a, b) => {
            const nomeA = a.getAttribute('data-nome') || '';
            const nomeB = b.getAttribute('data-nome') || '';

            // Se os nomes são diferentes, ordenar por nome
            if (nomeA !== nomeB) {
                return nomeA.localeCompare(nomeB);
            }

            // Se os nomes são iguais, ordenar por diâmetro numérico
            const diametroA = extractNumericValue(a.getAttribute('data-diametro') || '');
            const diametroB = extractNumericValue(b.getAttribute('data-diametro') || '');

            return diametroA - diametroB;
        });

        // Salvar a opção selecionada
        const selectedValue = select.value;

        // Remover todas as opções exceto a primeira
        while (select.options.length > 1) {
            select.remove(1);
        }

        // Adicionar as opções ordenadas de volta ao select
        options.forEach(option => {
            select.add(option);
        });

        // Restaurar a seleção anterior
        if (selectedValue) {
            select.value = selectedValue;
        }
    });
}
